:root {
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --bg-color: #fff;
    --sidebar-bg: #f7f8fa;
    --note-list-bg: #f7f8fa;
    --editor-bg: #fff;
    --border-color: #e8e8e8;
    --text-color: #333;
    --text-light: #888;
    --text-lighter: #b2b2b2;
    --primary-color: #3178e6;
    --hover-bg: #f0f1f2;
    --active-bg: #e6f0fa;
    --active-border: #3178e6;
    --vip-color: #e7a355;
    --icon-color: #555;
    --note-item-border: #f0f1f2;
    --note-icon-color: #4285f4;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    height: 100%;
    font-family: var(--font-family);
    font-size: 14px;
    color: var(--text-color);
    background-color: var(--bg-color);
    overflow: hidden;
}

#root {
    height: 100%;
}

.material-symbols-outlined {
    font-variation-settings:
    'FILL' 0,
    'wght' 300,
    'GRAD' 0,
    'opsz' 20;
    font-size: 20px;
    vertical-align: middle;
    flex-shrink: 0; /* Prevents icons from shrinking */
}

/* Material icon in dropdown items should be smaller and lighter */
.dropdown-item-icon .material-symbols-outlined {
    font-variation-settings:
    'FILL' 0,
    'wght' 300,
    'GRAD' 0,
    'opsz' 20;
    font-size: 18px;
    color: #909399;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.app-container {
    display: flex;
    height: 100vh;
    width: 100%;
    overflow: hidden;
    position: relative;
}

/* App container sidebar modes */
.app-container.sidebar-mode-full .sidebar {
    width: 250px;
    min-width: 250px;
    padding: 16px 0;
}

.app-container.sidebar-mode-partial .sidebar {
    width: 60px;
    min-width: 60px;
    padding: 16px 0;
}

.app-container.sidebar-mode-minimal .sidebar {
    width: 0;
    min-width: 0;
    padding: 0;
    border-right: none;
    overflow: hidden;
}

/* Hide sidebar toggle in minimal mode */
.app-container.sidebar-mode-minimal .sidebar-toggle {
    display: none;
}

/* --- Sidebar --- */
.sidebar {
    position: relative;
    transition: width 0.3s ease, min-width 0.3s ease, opacity 0.2s ease;
    width: 250px;
    min-width: 250px;
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    padding: 16px 0;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden; /* Prevent horizontal scrolling during transitions */
}

/* Sidebar modes */
.sidebar-mode-partial {
    width: 60px;
    min-width: 60px;
}

.sidebar-mode-minimal {
    width: 0;
    min-width: 0;
    padding: 0;
    border-right: none;
}

.sidebar.collapsed {
    width: 60px;
    min-width: 60px;
}

/* Improved sidebar collapse control */
.sidebar-collapse-control {
    position: fixed;
    bottom: 20px;
    left: 20px; /* Positioned on the left instead of centered */
    display: flex;
    background-color: var(--primary-color);
    border-radius: 30px;
    padding: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.25);
    z-index: 1000;
    width: auto; /* Auto width instead of fixed */
    justify-content: space-around;
    opacity: 0.9;
    transition: opacity 0.2s ease, transform 0.3s ease;
}

.sidebar-collapse-control:hover {
    opacity: 1;
}

/* Adjust position when sidebar is in minimal mode */
.app-container.sidebar-mode-minimal .sidebar-collapse-control {
    left: 10px;
}

.collapse-control-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: transparent;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s, transform 0.2s;
    margin: 0 2px;
}

.collapse-control-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.collapse-control-btn.active {
    background-color: white;
    color: var(--primary-color);
    transform: scale(1.05);
}

/* Improved button icon styles */
.collapse-control-btn .material-symbols-outlined {
    font-size: 20px;
    font-variation-settings:
    'FILL' 1,
    'wght' 400,
    'GRAD' 0,
    'opsz' 24;
}

/* Replace sidebar toggle with a more intuitive floating button */
.sidebar-toggle {
    position: absolute;
    top: 20px;
    right: -14px;
    width: 28px;
    height: 28px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    box-shadow: 0 2px 6px rgba(0,0,0,0.25);
    transition: transform 0.3s ease, background-color 0.2s;
    border: 2px solid white;
}

.sidebar-toggle:hover {
    background-color: #2a6ad9;
    transform: scale(1.1);
}

.sidebar-toggle .material-symbols-outlined {
    font-size: 18px;
    transition: transform 0.3s ease;
}

/* Rotate the toggle icon based on sidebar state */
.app-container.sidebar-mode-partial .sidebar-toggle .material-symbols-outlined,
.app-container.sidebar-mode-minimal .sidebar-toggle .material-symbols-outlined {
    transform: rotate(180deg);
}

/* Enhanced mode-specific styles */
.app-container.sidebar-mode-full .sidebar {
    width: 250px;
    min-width: 250px;
}

.app-container.sidebar-mode-partial .sidebar {
    width: 60px;
    min-width: 60px;
}

.app-container.sidebar-mode-minimal .sidebar {
    width: 0;
    min-width: 0;
    padding: 0;
    border-right: none;
}

/* Improved transition for sidebar content */
.sidebar-nav, .sidebar-profile, .sidebar-actions, .sidebar-footer {
    transition: opacity 0.2s ease;
}

.app-container.sidebar-mode-partial .sidebar-nav, 
.app-container.sidebar-mode-partial .sidebar-profile, 
.app-container.sidebar-mode-partial .sidebar-actions {
    opacity: 0.95;
}

/* Tooltips for sidebar items in partial mode */
.app-container.sidebar-mode-partial .nav-item,
.app-container.sidebar-mode-partial .folder-item {
    position: relative;
}

.app-container.sidebar-mode-partial .nav-item:hover::after,
.app-container.sidebar-mode-partial .folder-item:hover::after {
    content: attr(data-title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background-color: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 10px;
    opacity: 0;
    animation: fadeIn 0.2s ease forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

/* Hover effect for sidebar in minimal mode */
.app-container.sidebar-mode-minimal .sidebar-collapse-control:hover {
    transform: translateX(5px);
}

/* Ensure the content adjusts properly with sidebar changes */
.app-container.sidebar-mode-partial .note-list-panel,
.app-container.sidebar-mode-minimal .note-list-panel {
    border-left: 1px solid var(--border-color);
}

/* Show minimal sidebar toggle button when in minimal mode */
.app-container.sidebar-mode-minimal .sidebar-toggle {
    right: -14px;
    background-color: var(--primary-color);
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

/* Override the previous rule that hides the toggle in minimal mode */
.app-container.sidebar-mode-minimal .sidebar-toggle {
    display: flex;
}

.hidden {
    display: none !important;
}

.app-container.sidebar-collapsed .note-list-panel {
    width: 300px;
}

.sidebar-profile {
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 10px 16px 20px;
}

.sidebar-profile .avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #d8d8d8;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-profile .avatar .material-symbols-outlined {
    font-size: 40px;
    color: #fff;
}

.sidebar-profile .vip-status {
    font-size: 12px;
    color: var(--vip-color);
    border: 1px solid var(--vip-color);
    border-radius: 4px;
    padding: 2px 6px;
    margin-top: 5px;
}

.sidebar-actions {
    padding: 0 16px;
    margin-bottom: 20px;
}

.new-note-btn {
    width: 100%;
    padding: 10px;
    background-color: var(--primary-color);
    color: #fff;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.new-note-btn:hover {
    background-color: #2a6ad9;
}

.new-note-btn .material-symbols-outlined {
    margin-right: 8px;
}

.sidebar-nav {
    flex-grow: 1;
    overflow-y: auto;
    padding: 0 8px;
}

.nav-item, .folder-item, .sub-folder-item {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 2px;
    gap: 10px;
}

.nav-item .material-symbols-outlined,
.folder-item .material-symbols-outlined {
    color: var(--icon-color);
}

.item-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
}

.nav-item:hover, .folder-item:hover, .sub-folder-item:hover {
    background-color: var(--hover-bg);
}

.nav-item.active, .sub-folder-item.active, .folder-item.active {
    background-color: var(--active-bg);
}

.nav-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

.folder-item .folder-arrow {
    transition: transform 0.2s;
}

.folder-item.open .folder-arrow {
    transform: rotate(0deg); /* reset from previous state */
}

.sub-folder-list {
    padding-left: 20px;
}

.sub-folder-item {
    padding-left: 16px;
}


.sidebar-footer {
    padding: 16px;
    border-top: 1px solid var(--border-color);
    font-size: 12px;
    color: var(--text-light);
}

.sidebar-footer-links {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.sidebar-footer-links a {
    color: var(--text-light);
    text-decoration: none;
    display: flex;
    align-items: center;
}
.sidebar-footer-links a:hover {
    color: var(--primary-color);
}
.sidebar-footer-links a .material-symbols-outlined {
    font-size: 16px;
    margin-right: 4px;
}

/* --- Note List Panel --- */
.note-list-panel {
    width: 300px;
    min-width: 300px;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    background-color: var(--note-list-bg);
    position: relative;
    transition: margin-left 0.3s ease;
}

.note-list-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
}

.search-bar {
    width: 100%;
    padding: 8px 12px 8px 30px;
    border: none;
    border-radius: 4px;
    background-color: #eaecef;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23888" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="M21 21L16.65 16.65"/></svg>');
    background-repeat: no-repeat;
    background-position: 8px center;
    font-size: 14px;
}

.search-bar:focus {
    outline: none;
    background-color: #e2e4e7;
}

.note-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 0;
}

.note-item {
    position: relative;
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid var(--note-item-border);
    background-color: #fff;
    transition: background-color 0.2s;
}

.note-item:hover {
    background-color: var(--hover-bg);
}

.note-item.active {
    background-color: var(--active-bg);
    border-left: 3px solid var(--primary-color);
}

.note-item h3 {
    font-size: 15px;
    margin: 0 0 6px 0;
    font-weight: 500;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.note-item h3::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/><path d="M14 3v5h5M16 13H8M16 17H8M10 9H8"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

.note-item-meta {
    font-size: 12px;
    color: var(--text-lighter);
    margin-top: 6px;
}

.note-item-snippet {
    font-size: 13px;
    color: var(--text-light);
    margin: 4px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.folder-info {
    padding: 10px 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.folder-name-header {
    font-size: 16px;
    font-weight: 500;
}

.back-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 10px;
    border: none;
    background: none;
    cursor: pointer;
    color: var(--text-light);
    padding: 0;
}

.back-btn:hover {
    color: var(--primary-color);
}

.back-btn .material-symbols-outlined {
    font-size: 16px;
}

.note-list-count {
    padding: 8px 16px;
    font-size: 12px;
    color: var(--text-light);
    border-bottom: 1px solid var(--border-color);
}

/* 更多按钮样式 */
.note-item-actions {
    position: absolute;
    right: 16px;
    top: 12px;
    display: none;
    z-index: 100;
}

.note-item:hover .note-item-actions {
    display: block;
}

.note-item-more {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 2px;
    font-size: 18px;
}

.note-item-more:hover {
    color: var(--primary-color);
}

/* 下拉菜单样式 */
.note-dropdown-menu {
    position: absolute;
    right: 0;
    top: 100%;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    z-index: 1001;
    min-width: 120px;
    padding: 5px 0;
}

.note-dropdown-menu.hidden {
    display: none;
}

.dropdown-item {
    padding: 0 16px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    color: #606266;
    position: relative;
    height: 34px;
    line-height: 34px;
}

.dropdown-item:hover {
    background-color: #f5f7fa;
}

.dropdown-item-icon {
    width: 18px;
    height: 18px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #909399;
}

.dropdown-item-text {
    flex-grow: 1;
}

/* 右侧箭头样式 */
.dropdown-item.has-submenu::after {
    content: '›';
    position: absolute;
    right: 10px;
    font-size: 16px;
    color: #c0c4cc;
}

/* 为新建和更多选项添加右侧箭头 */
.dropdown-item[data-action="new"]::after,
.dropdown-item[data-action="more"]::after {
    content: '›';
    position: absolute;
    right: 10px;
    font-size: 16px;
    color: #c0c4cc;
}

/* --- Editor Panel --- */
.editor-panel {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; /* 允许flex项目缩小到内容宽度以下 */
    width: 100%; /* 确保编辑器面板占据全宽 */
    height: 100vh; /* 确保编辑器面板占据全高 */
    overflow: hidden; /* 防止内容溢出影响布局 */
}

.editor-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    flex-shrink: 0; /* 防止头部被压缩 */
    min-height: 60px; /* 确保头部有固定的最小高度 */
    width: 100%; /* 确保头部占据全宽 */
    box-sizing: border-box;
}

.editor-header-title {
    flex-grow: 1;
    margin-right: 20px;
}

.editor-title-input {
    width: 100%;
    border: none;
    outline: none;
    background: transparent;
    font-family: inherit;
    font-size: 22px;
    font-weight: 600;
    color: var(--text-color);
    padding: 4px 0;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s ease-in-out;
}

.editor-title-input:focus {
    border-bottom-color: var(--primary-color);
}

.editor-header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.save-status {
    display: flex;
    align-items: center;
    color: var(--text-light);
    font-size: 13px;
    white-space: nowrap;
}
.save-status .material-symbols-outlined {
    font-size: 16px;
    color: green;
    margin-right: 4px;
}


.editor-header-actions .btn {
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    background: none;
    border: 1px solid #ccc;
    display: flex;
    align-items: center;
    gap: 6px;
}

.editor-header-actions .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}
.editor-header-actions .btn.icon-btn {
    padding: 6px;
    border: none;
}
.editor-header-actions .btn:hover {
    background-color: var(--hover-bg);
}
.editor-header-actions .btn-primary:hover {
    background-color: #2a6ad9;
}


/* --- Editor Toolbar --- */
.editor-toolbar {
    padding: 4px 8px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    flex-wrap: nowrap; /* 防止工具栏按钮换行 */
    gap: 2px;
    background-color: #f9f9f9;
    overflow-x: auto; /* 当工具栏内容过多时显示水平滚动条 */
    min-height: 36px; /* 确保工具栏有固定的最小高度 */
    flex-shrink: 0; /* 防止工具栏被压缩 */
    width: 100%; /* 确保工具栏占据全宽 */
    box-sizing: border-box;
}

.toolbar-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #444;
    font-size: 13px;
    height: 28px;
    min-width: 28px;
    flex-shrink: 0; /* 防止按钮被压缩 */
    white-space: nowrap; /* 防止按钮文字换行 */
}

.toolbar-btn:hover {
    background-color: var(--hover-bg);
}

.toolbar-btn.active {
    background-color: var(--active-bg);
    color: var(--primary-color);
}

.toolbar-separator {
    width: 1px;
    height: 20px;
    background-color: #e0e0e0;
    margin: 0 4px;
    flex-shrink: 0; /* 防止分隔符被压缩 */
}

.toolbar-dropdown {
    position: relative;
    display: inline-block;
    flex-shrink: 0; /* 防止下拉菜单被压缩 */
}

.toolbar-dropdown .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
}

.dropdown-label {
    margin: 0 2px;
}

.dropdown-arrow {
    font-size: 18px !important;
    margin-left: -4px;
}

.font-size-btn {
    min-width: 50px;
    padding: 4px 10px;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.more-btn {
    margin-left: 4px;
}

.editor-content-wrapper {
    flex-grow: 1;
    overflow-y: auto;
    padding: 24px 48px;
    min-height: 0; /* 允许flex项目缩小 */
    width: 100%; /* 确保内容包装器占据全宽 */
    box-sizing: border-box;
    position: relative; /* 为AI悬浮按钮提供定位上下文 */
}

.editor-content {
    max-width: 800px;
    margin: 0 auto;
    outline: none;
    line-height: 1.7;
}

/* 字体样式支持 - 确保内联样式能够正确显示 */
.editor-content [style*="font-family"] {
    /* 内联样式会自动应用，这里只是确保优先级 */
}

.editor-content h1 { font-size: 2em; }
.editor-content h2 { font-size: 1.5em; }
.editor-content h3 { font-size: 1.17em; }
.editor-content b { font-weight: bold; }
.editor-content i { font-style: italic; }
.editor-content u { text-decoration: underline; }
.editor-content s { text-decoration: line-through; }

/* 字体大小样式 */
.editor-content font[size="1"] { font-size: 8pt; }
.editor-content font[size="2"] { font-size: 10pt; }
.editor-content font[size="3"] { font-size: 12pt; }
.editor-content font[size="4"] { font-size: 14pt; }
.editor-content font[size="5"] { font-size: 18pt; }
.editor-content font[size="6"] { font-size: 24pt; }
.editor-content font[size="7"] { font-size: 36pt; }

.editor-content .todo-item {
    display: flex;
    align-items: center;
    list-style: none;
    margin-left: -40px; /* Counteract ul padding */
}

.editor-content .todo-item input[type="checkbox"] {
    margin-right: 10px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-light);
    text-align: center;
}

.empty-state .material-symbols-outlined {
    font-size: 80px;
    margin-bottom: 20px;
}

.empty-state h2 {
    font-size: 20px;
    margin-bottom: 10px;
}

/* --- Custom Scrollbar --- */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
  border-radius: 10px;
  border: 2px solid var(--sidebar-bg); /* Creates padding around thumb */
  background-clip: content-box;
}
.note-list ::-webkit-scrollbar-thumb, .editor-content-wrapper ::-webkit-scrollbar-thumb {
   border: 2px solid var(--note-list-bg);
   background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}

/* --- Rename Input Field --- */
.rename-input {
    width: 100%;
    font-family: inherit;
    font-weight: inherit;
    color: inherit;
    padding: 2px 4px;
    margin: -2px -4px; /* Negative margin to align perfectly */
    border: 1px solid var(--primary-color);
    border-radius: 3px;
    outline: none;
    background-color: #fff;
    box-sizing: border-box;
}

.note-item .rename-input {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 6px;
}

/* 下拉菜单样式 */
.dropdown-menu {
    min-width: 120px;
    max-height: 300px;
    overflow-y: auto;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
}

.dropdown-item:hover {
    background-color: #f5f5f5;
}

.dropdown-item-icon {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    display: inline-block;
}

.dropdown-item-text {
    flex-grow: 1;
}

/* 字体下拉菜单项样式 */
.font-dropdown .dropdown-item {
    padding: 8px 12px;
}

/* 格式下拉菜单项样式 */
.format-dropdown .dropdown-item {
    padding: 8px 12px;
}

/* 字体大小下拉菜单样式 */
.font-size-dropdown {
    width: 120px;
    text-align: center;
}

.font-size-dropdown .dropdown-item {
    padding: 10px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 预览字体大小效果 */
.font-size-dropdown .dropdown-item:hover {
    background-color: #f0f0f0;
}

/* 字体大小按钮样式优化 */
.font-size-btn {
    min-width: 50px;
    padding: 4px 10px;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* 添加下拉指示器 */
.font-size-btn::after {
    content: '';
    display: inline-block;
    margin-left: 5px;
    border-top: 4px solid #666;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
}

/* 插入下拉菜单项样式 */
.insert-dropdown .dropdown-item {
    padding: 8px 12px;
}

/* 自定义滚动条 */
.dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 自定义对话框样式 */
.custom-dialog {
    font-family: var(--font-family);
    animation: dialog-fade-in 0.2s ease-out;
}

.custom-dialog h3 {
    color: var(--text-color);
}

.custom-dialog input {
    font-family: var(--font-family);
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.custom-dialog input:focus {
    border-color: var(--primary-color) !important;
}

.custom-dialog button {
    font-family: var(--font-family);
    font-size: 14px;
    transition: all 0.2s;
}

.custom-dialog button:hover {
    opacity: 0.9;
}

@keyframes dialog-fade-in {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* 颜色选择器样式 */
.color-picker-wrapper {
    position: relative;
}

.color-picker-popup {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 10px;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 5px;
}

.color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #ddd;
}

.color-swatch:hover {
    transform: scale(1.1);
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

/* 前景色和背景色按钮样式 */
.toolbar-btn[data-command="foreColor"],
.toolbar-btn[data-command="hiliteColor"] {
    position: relative;
}

.toolbar-btn[data-command="foreColor"]::after,
.toolbar-btn[data-command="hiliteColor"]::after {
    content: '';
    display: block;
    position: absolute;
    bottom: 3px;
    left: 50%;
    transform: translateX(-50%);
    width: 14px;
    height: 3px;
    border-radius: 1px;
}

.toolbar-btn[data-command="foreColor"]::after {
    background-color: currentColor;
}

.toolbar-btn[data-command="hiliteColor"]::after {
    background-color: #ffeb3b;
}

/* 自定义颜色输入框 */
.custom-color-input {
    grid-column: span 8;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.custom-color-input input[type="color"] {
    width: 30px;
    height: 30px;
    border: none;
    padding: 0;
    background: none;
}

.custom-color-input input[type="text"] {
    flex: 1;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* 链接对话框样式 */
.link-dialog {
    width: 450px;
    max-width: 90vw;
}

.link-dialog h3 {
    margin-bottom: 20px;
    font-size: 18px;
    color: var(--text-color);
}

.link-dialog input {
    transition: border-color 0.2s;
}

.link-dialog input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(49, 120, 230, 0.1);
}

.link-dialog label {
    font-weight: 500;
}

/* 链接预览区域 */
.link-preview {
    margin-top: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 13px;
    word-break: break-all;
}

.link-preview-title {
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--text-light);
    font-size: 12px;
}

/* 链接按钮样式 */
.toolbar-btn[data-command="createLink"] {
    position: relative;
}

.toolbar-btn[data-command="createLink"].active {
    background-color: var(--active-bg);
    color: var(--primary-color);
}

/* 链接对话框按钮样式 */
.link-dialog-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.link-dialog-buttons button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.link-dialog-buttons button:hover {
    opacity: 0.9;
}

.link-dialog-buttons button#cancel-link {
    background-color: #f1f1f1;
    color: #333;
}

.link-dialog-buttons button#confirm-link {
    background-color: var(--primary-color);
    color: white;
}

/* 对话框和遮罩层样式 */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.custom-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 确保所有对话框中的输入框样式一致 */
.custom-dialog input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.custom-dialog input[type="text"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(49, 120, 230, 0.1);
}

.custom-dialog label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-color);
}

.link-checkbox {
    margin: 15px 0;
}

.link-checkbox label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
}

.link-checkbox input[type="checkbox"] {
    margin-right: 8px;
}

/* 确保对话框中的按钮样式一致 */
.custom-dialog button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.custom-dialog button:hover {
    opacity: 0.9;
}

#cancel-link {
    background-color: #f1f1f1;
    color: #333;
}

#confirm-link {
    background-color: var(--primary-color);
    color: white;
}

/* 编辑器中链接样式 */
.editor-content a {
    color: #3178e6;
    text-decoration: underline;
    cursor: pointer;
}

.editor-content a:hover {
    color: #1a56c4;
    text-decoration: underline;
}

/* 移除链接按钮样式 */
.remove-link-btn {
    background-color: #f44336;
    color: white;
    margin-right: auto;
}

/* Delete button styling */
/* Ensure folder items and sub-folder items are positioned for absolute or auto margin */
.folder-item, .sub-folder-item {
    position: relative;
}

/* Style for delete-folder button */
.delete-folder-btn {
    margin-left: auto;
    cursor: pointer;
    color: #ff4d4f;
    font-size: 18px;
    padding: 0 4px;
}
.delete-folder-btn:hover {
    color: #f5222d;
}

/* Style for note items: make room for delete button */
.note-item {
    position: relative;
    padding-right: 40px;
}

/* Style for delete-note button */
.delete-note-btn {
    position: absolute;
    top: 12px;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    color: #ff4d4f;
    font-size: 16px;
    padding: 0;
}
.delete-note-btn:hover {
    color: #f5222d;
}

/* Confirm dialog overlay */
.confirm-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 2000;
}

/* Confirm dialog container */
.confirm-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
    padding: 24px;
    width: 400px;
    max-width: 90%;
    text-align: center;
    z-index: 2001;
}

/* Dialog header */
.confirm-dialog-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.confirm-dialog-header .dialog-icon {
    font-size: 36px;
    color: #faad14;
    margin-right: 8px;
}

.dialog-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

/* Dialog message */
.dialog-message {
    font-size: 14px;
    color: #555;
    margin-bottom: 24px;
}

/* Dialog buttons */
.dialog-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
}

.dialog-buttons .cancel-btn {
    padding: 8px 16px;
    background: #f5f5f5;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    color: #333;
}

.dialog-buttons .cancel-btn:hover {
    background: #e6e6e6;
}

.dialog-buttons .confirm-btn {
    padding: 8px 16px;
    background: #1890ff;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
}

.dialog-buttons .confirm-btn:hover {
    background: #40a9ff;
}

/* Style for rename-note button */
.rename-note-btn {
    position: absolute;
    top: 12px;
    right: 44px;
    background: none;
    border: none;
    cursor: pointer;
    color: #1890ff;
    font-size: 16px;
    padding: 0;
}
.rename-note-btn:hover {
    color: #40a9ff;
}

/* 文件夹按钮样式 */
.folder-item, .sub-folder-item {
    position: relative;
}

.rename-folder-btn {
    display: none;
    cursor: pointer;
    color: #1890ff;
    font-size: 16px;
    margin-left: 4px;
}

.folder-item:hover .rename-folder-btn,
.sub-folder-item:hover .rename-folder-btn {
    display: inline-block;
}

.rename-folder-btn:hover {
    color: #40a9ff;
}

/* 删除文件夹按钮样式 */
.delete-folder-btn {
    display: none;
    cursor: pointer;
    color: #ff4d4f;
    font-size: 16px;
    margin-left: 4px;
}

.folder-item:hover .delete-folder-btn,
.sub-folder-item:hover .delete-folder-btn {
    display: inline-block;
}

.delete-folder-btn:hover {
    color: #f5222d;
}

/* 错误信息样式 */
.error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #f44336;
    color: white;
    padding: 12px 16px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    z-index: 1001;
    animation: slideIn 0.3s ease-out, fadeOut 0.5s ease-out 4.5s forwards;
}

.error-message span {
    margin-right: 8px;
}

.close-error-btn {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    margin-left: 16px;
    padding: 0;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; visibility: hidden; }
}

/* 根据不同的模式设置内容显示和隐藏 */
.sidebar-mode-partial .item-name,
.sidebar-mode-partial .folder-arrow,
.sidebar-mode-minimal .item-name,
.sidebar-mode-minimal .folder-arrow {
    display: none;
}

.sidebar-mode-partial .sidebar-profile span,
.sidebar-mode-partial .sidebar-profile .vip-status,
.sidebar-mode-minimal .sidebar-profile span,
.sidebar-mode-minimal .sidebar-profile .vip-status,
.sidebar-mode-minimal .sidebar-profile,
.sidebar-mode-minimal .sidebar-actions,
.sidebar-mode-minimal .sidebar-nav,
.sidebar-mode-minimal .sidebar-footer {
    display: none;
}

/* 调整部分收缩模式下的按钮样式 */
.app-container.sidebar-mode-partial .sidebar-profile {
    align-items: center;
}

.app-container.sidebar-mode-partial .sidebar-actions button {
    width: 40px;
    height: 40px;
    padding: 8px;
    border-radius: 50%;
    justify-content: center;
    margin: 5px auto;
}

.app-container.sidebar-mode-partial .sidebar-actions button .material-symbols-outlined {
    margin-right: 0;
}

.app-container.sidebar-mode-partial .nav-item,
.app-container.sidebar-mode-partial .folder-item,
.app-container.sidebar-mode-partial .sub-folder-item {
    justify-content: center;
    padding: 12px 0;
}

/* 隐藏最小化模式下的所有内容 */
/* .app-container.sidebar-mode-minimal .sidebar * {
    visibility: hidden;
} */

/* 保持底部控制按钮可见 */
/* .sidebar-collapse-control {
    visibility: visible !important;
} */

/* Hide items based on sidebar mode */
.app-container.sidebar-mode-partial .item-name,
.app-container.sidebar-mode-partial .folder-arrow,
.app-container.sidebar-mode-minimal .item-name,
.app-container.sidebar-mode-minimal .folder-arrow {
    display: none;
}

.app-container.sidebar-mode-partial .sidebar-profile span,
.app-container.sidebar-mode-partial .sidebar-profile .vip-status,
.app-container.sidebar-mode-minimal .sidebar-profile span,
.app-container.sidebar-mode-minimal .sidebar-profile .vip-status,
.app-container.sidebar-mode-minimal .sidebar-profile,
.app-container.sidebar-mode-minimal .sidebar-actions,
.app-container.sidebar-mode-minimal .sidebar-nav,
.app-container.sidebar-mode-minimal .sidebar-footer {
    display: none;
}

.app-container.sidebar-mode-partial .new-note-btn,
.app-container.sidebar-mode-minimal .new-note-btn {
    padding: 8px;
    justify-content: center;
}

.app-container.sidebar-mode-partial .new-note-btn .material-symbols-outlined,
.app-container.sidebar-mode-minimal .new-note-btn .material-symbols-outlined {
    margin-right: 0;
}

.app-container.sidebar-mode-partial .nav-item,
.app-container.sidebar-mode-partial .folder-item,
.app-container.sidebar-mode-partial .sub-folder-item,
.app-container.sidebar-mode-minimal .nav-item,
.app-container.sidebar-mode-minimal .folder-item,
.app-container.sidebar-mode-minimal .sub-folder-item {
    justify-content: center;
    padding: 12px 0;
}

/* Notification badge for sidebar items when collapsed */
.app-container.sidebar-mode-partial .nav-item .badge,
.app-container.sidebar-mode-partial .folder-item .badge {
    position: absolute;
    top: 4px;
    right: 4px;
    background-color: #ff4d4f;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Improve transitions for sub-folder lists */
.sub-folder-list {
    padding-left: 20px;
    transition: max-height 0.3s ease, opacity 0.2s ease;
    overflow: hidden;
}

.app-container.sidebar-mode-partial .sub-folder-list,
.app-container.sidebar-mode-minimal .sub-folder-list {
    max-height: 0;
    opacity: 0;
    padding-left: 0;
}

/* Ensure proper alignment in partial mode */
.app-container.sidebar-mode-partial .sidebar-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Improve animation for sidebar toggle button */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.app-container.sidebar-mode-minimal .sidebar-toggle {
    animation: pulse 2s infinite;
}

/* Handling the width transition for note list panel */
.note-list-panel {
    transition: margin-left 0.3s ease;
}

.app-container.sidebar-mode-minimal .note-list-panel {
    margin-left: 0;
    border-left: none;
}

.app-container.sidebar-mode-partial .note-list-panel {
    margin-left: 60px;
}

.app-container.sidebar-mode-full .note-list-panel {
    margin-left: 250px;
}

/* Keyboard shortcut tooltip */
.sidebar-toggle:hover::after {
    content: "Ctrl+B";
    position: absolute;
    right: 105%;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 3px 6px;
    border-radius: 3px;
    font-size: 11px;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
    white-space: nowrap;
    z-index: 10;
}

@media (max-width: 768px) {
    .sidebar-collapse-control {
        bottom: 10px;
        left: 10px;
    }
    
    .app-container.sidebar-mode-full .sidebar {
        width: 200px;
        min-width: 200px;
    }
}

/* 修复最小化模式下的白屏问题 */
.app-container.sidebar-mode-minimal .note-list-panel {
    margin-left: 0;
    border-left: none;
}

/* 确保最小化模式下内容正确显示 */
.app-container.sidebar-mode-minimal .sidebar {
    width: 0;
    min-width: 0;
    padding: 0;
    border-right: none;
    overflow: hidden;
    visibility: hidden;
}

/* 确保最小化模式下的侧边栏切换按钮可见 */
.app-container.sidebar-mode-minimal .sidebar-toggle {
    right: -14px;
    visibility: visible;
    opacity: 1;
    z-index: 100;
    position: fixed;
    left: 10px;
    top: 20px;
}

/* 修复侧边栏折叠控制面板在最小化模式下的位置 */
.app-container.sidebar-mode-minimal .sidebar-collapse-control {
    left: 10px;
    visibility: visible !important;
    z-index: 100;
}

/* 修复note-list-panel在不同模式下的位置 */
.note-list-panel {
    transition: margin-left 0.3s ease;
    margin-left: 0;
}

.app-container.sidebar-mode-full .note-list-panel {
    margin-left: 250px;
}

.app-container.sidebar-mode-partial .note-list-panel {
    margin-left: 60px;
}

/* 修复内容区域的布局 */
.app-container {
    display: flex;
    height: 100vh;
    width: 100%;
    overflow: hidden;
    position: relative;
}

/* 确保sidebar-toggle在最小化模式下可见并可点击 */
.sidebar-toggle {
    z-index: 1000;
}

/* 修复Mac系统上的Safari浏览器兼容性问题 */
@supports (-webkit-touch-callout: none) {
    .app-container.sidebar-mode-minimal .sidebar-toggle,
    .app-container.sidebar-mode-minimal .sidebar-collapse-control {
        position: fixed;
    }
}

/* 最小化模式下的侧边栏切换按钮 */
.sidebar-minimal-toggle {
    position: fixed;
    top: 20px;
    left: 10px;
    width: 28px;
    height: 28px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 2px 6px rgba(0,0,0,0.25);
    transition: transform 0.3s ease, background-color 0.2s;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

.sidebar-minimal-toggle:hover {
    background-color: #2a6ad9;
    transform: scale(1.1);
}

.sidebar-minimal-toggle .material-symbols-outlined {
    font-size: 18px;
}

/* 最小化模式下的侧边栏切换按钮的键盘快捷键提示 */
.sidebar-minimal-toggle:hover::after {
    content: "Ctrl+B";
    position: absolute;
    right: -45px;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 3px 6px;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 10;
}

/* 修复note-list-panel在不同模式下的位置 */
.note-list-panel {
    width: 300px;
    min-width: 300px;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    background-color: var(--note-list-bg);
    position: relative;
    transition: margin-left 0.3s ease;
}

/* 不同模式下note-list-panel的位置 */
.app-container.sidebar-mode-full .note-list-panel {
    margin-left: 0; /* 不需要margin，因为sidebar已经占据了空间 */
}

.app-container.sidebar-mode-partial .note-list-panel {
    margin-left: 0; /* 不需要margin，因为sidebar已经占据了空间 */
}

.app-container.sidebar-mode-minimal .note-list-panel {
    margin-left: 0; /* 最小化模式下不需要margin */
    border-left: none; /* 移除左边框 */
}

/* 修复内容区域的布局 */
.app-container {
    display: flex;
    height: 100vh;
    width: 100%;
    overflow: hidden;
    position: relative;
}

/* 确保在最小化模式下note-list-panel正确显示 */
.app-container.sidebar-mode-minimal .editor-panel {
    flex-grow: 1;
    margin-left: 0;
}

/* 最小化模式下的布局调整 */
.app-container.sidebar-mode-minimal .note-list-panel {
    display: none; /* 完全隐藏笔记列表面板 */
}

.app-container.sidebar-mode-minimal .editor-panel {
    flex-grow: 1;
    width: 100%; /* 让编辑区域占据整个宽度 */
    margin-left: 0;
}

/* 确保最小化模式下底部控制按钮保持可见 */
.app-container.sidebar-mode-minimal .sidebar-collapse-control {
    position: fixed;
    bottom: 20px;
    left: 20px;
    opacity: 0.9;
    z-index: 1000;
    background-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    padding: 5px;
    border-radius: 30px;
    visibility: visible !important;
    transition: opacity 0.3s ease, transform 0.3s ease;
    border: none; /* 移除边框 */
}

.app-container.sidebar-mode-minimal .sidebar-collapse-control:hover {
    opacity: 1;
    transform: scale(1.05);
}

/* 最小化模式下的侧边栏切换按钮可以隐藏 */
.app-container.sidebar-mode-minimal .sidebar-minimal-toggle {
    display: none; /* 不需要显示侧边栏切换按钮 */
}

/* 添加一个固定在左下角的展开按钮 */
.expand-button {
    position: fixed;
    left: 20px;
    bottom: 80px; /* 位于底部控制按钮上方 */
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    z-index: 1000;
    transition: transform 0.2s ease, background-color 0.2s ease;
}

.expand-button:hover {
    transform: scale(1.1);
    background-color: #2a6ad9;
}

.expand-button .material-symbols-outlined {
    font-size: 24px;
}

/* 添加展开按钮的工具提示 */
.expand-button:hover::after {
    content: "展开侧边栏 (Ctrl+B)";
    position: absolute;
    left: 50px;
    white-space: nowrap;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1001;
}

/* 添加一个小型的悬浮按钮，用于在最小化模式下显示/隐藏控制面板 */
.toggle-controls-button {
    position: fixed;
    left: 20px;
    bottom: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    z-index: 1001;
    transition: transform 0.2s ease, background-color 0.2s ease;
    border: none;
}

.toggle-controls-button:hover {
    transform: scale(1.1);
    background-color: #2a6ad9;
}

.toggle-controls-button .material-symbols-outlined {
    font-size: 20px;
}

/* 控制面板隐藏状态 */
.controls-hidden .sidebar-collapse-control {
    opacity: 0;
    visibility: hidden !important;
    pointer-events: none;
}

/* 移除绿色边框 */
.app-container.sidebar-mode-minimal .sidebar-collapse-control {
    outline: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    border: none;
}

/* ===== AI对话功能样式 ===== */

/* AI悬浮按钮 */
.ai-float-btn {
    position: fixed; /* 改为固定定位，相对于视口定位 */
    bottom: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    z-index: 1000;
}

.ai-float-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.ai-float-btn .material-symbols-outlined {
    font-size: 24px;
    font-variation-settings: 'FILL' 1;
}

/* AI悬浮按钮在不同侧边栏模式下的定位调整 */
.app-container.sidebar-mode-full .ai-float-btn {
    right: 20px; /* 全展开模式：正常右侧位置 */
}

.app-container.sidebar-mode-partial .ai-float-btn {
    right: 20px; /* 部分收缩模式：正常右侧位置 */
}

.app-container.sidebar-mode-minimal .ai-float-btn {
    right: 20px; /* 最小化模式：正常右侧位置 */
}

/* AI聊天面板在不同侧边栏模式下的定位调整 */
.app-container.sidebar-mode-full .ai-chat-panel {
    right: 20px; /* 全展开模式：正常右侧位置 */
}

.app-container.sidebar-mode-partial .ai-chat-panel {
    right: 20px; /* 部分收缩模式：正常右侧位置 */
}

.app-container.sidebar-mode-minimal .ai-chat-panel {
    right: 20px; /* 最小化模式：正常右侧位置 */
}

/* AI对话面板 */
.ai-chat-panel {
    position: fixed !important; /* 强制固定定位，相对于视口定位 */
    bottom: 90px !important;
    right: 20px !important;
    width: 400px;
    height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.95);
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    z-index: 999;
    border: 1px solid var(--border-color);
    /* 防止被其他元素影响 */
    margin: 0 !important;
    left: auto !important;
    top: auto !important;
}

.ai-chat-panel.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* AI对话头部 */
.ai-chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.ai-chat-header h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.ai-chat-controls {
    display: flex;
    gap: 8px;
}

.ai-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.ai-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.ai-btn .material-symbols-outlined {
    font-size: 18px;
}

/* AI消息区域 */
.ai-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    scroll-behavior: smooth;
    /* 优化滚动性能 */
    will-change: scroll-position;
    /* 防止内容变化时的布局跳动 */
    contain: layout style;
}

.ai-chat-messages::-webkit-scrollbar {
    width: 6px;
}

.ai-chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.ai-chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.ai-chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 欢迎消息 */
.ai-welcome-message {
    display: flex;
    gap: 12px;
    align-items: flex-start;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: 16px;
    border: 1px solid #e1e5ff;
    margin-bottom: 16px;
    animation: welcomeSlideIn 0.5s ease-out;
}

@keyframes welcomeSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ai-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ai-avatar .material-symbols-outlined {
    font-size: 20px;
    font-variation-settings: 'FILL' 1;
}

.ai-message-content {
    flex: 1;
}

.ai-message-content p {
    margin: 0 0 12px 0;
    line-height: 1.5;
    color: var(--text-color);
    font-size: 14px;
}

.ai-message-content ul {
    margin: 0 0 12px 20px;
    color: var(--text-color);
}

.ai-message-content li {
    margin-bottom: 6px;
    line-height: 1.4;
    font-size: 13px;
    color: var(--text-light);
}

/* 聊天消息 */
.chat-message {
    display: flex;
    gap: 8px;
    align-items: flex-end;
    margin-bottom: 16px;
    animation: messageSlideIn 0.3s ease-out;
}

.chat-message.user-message {
    flex-direction: row-reverse;
    justify-content: flex-start;
}

.message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-bottom: 4px;
}

.message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-avatar {
    background: var(--primary-color) !important;
    color: white;
}

.message-avatar .material-symbols-outlined {
    font-size: 16px;
}

/* 消息气泡 */
.message-bubble {
    max-width: 75%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ai-bubble {
    background: #f1f3f5;
    color: #2c3e50;
    border-bottom-left-radius: 4px;
}

.user-bubble {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 4px;
}

.message-text {
    line-height: 1.5;
    margin-bottom: 6px;
    color: inherit;
    font-size: 14px;
}

.message-text.streaming {
    position: relative;
}

.message-text p {
    margin: 0 0 8px 0;
}

.message-text p:last-child {
    margin-bottom: 0;
}

.message-text strong {
    font-weight: 600;
}

.message-text em {
    font-style: italic;
}

.message-text code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
}

.user-bubble .message-text code {
    background: rgba(255, 255, 255, 0.2);
}

/* 打字机光标效果 */
.typing-cursor {
    display: inline-block;
    width: 2px;
    height: 1em;
    background: var(--primary-color);
    margin-left: 2px;
    animation: blink 1.2s infinite;
    vertical-align: baseline;
    border-radius: 1px;
    /* 优化动画性能 */
    will-change: opacity;
}

@keyframes blink {
    0%, 45% { opacity: 1; }
    50%, 100% { opacity: 0.3; }
}

/* 流式文本效果 */
.message-text.streaming {
    position: relative;
    min-height: 20px; /* 防止高度变化导致的闪动 */
}

.message-text.streaming::after {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, transparent 0%, rgba(102, 126, 234, 0.3) 50%, transparent 100%);
    animation: streamingGlow 2s ease-in-out infinite;
}

@keyframes streamingGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

/* 优化消息渲染性能 */
.chat-message {
    contain: layout style; /* CSS containment 优化渲染性能 */
}

.message-text {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    /* 防止文本变化时的布局跳动 */
    line-height: 1.5;
    min-height: 1.5em;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 6px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.message-bubble:hover .message-meta {
    opacity: 1;
}

.message-time {
    font-size: 11px;
    color: var(--text-lighter);
    opacity: 0.8;
}

.user-bubble .message-time {
    color: rgba(255, 255, 255, 0.8);
}

.message-actions {
    display: flex;
    gap: 4px;
}

.message-action-btn {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.message-action-btn:hover {
    background: var(--hover-bg);
    color: var(--text-color);
    opacity: 1;
}

.message-action-btn .material-symbols-outlined {
    font-size: 14px;
}

/* AI生成指示器 */
.ai-generating-indicator {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    margin-bottom: 12px;
    border: 1px solid var(--border-color);
}

.generating-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.generating-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: bounce 1.4s infinite ease-in-out both;
}

.generating-dots span:nth-child(1) { animation-delay: -0.32s; }
.generating-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.generating-text {
    font-size: 13px;
    color: var(--text-light);
    margin-left: 8px;
}

.stop-generating-btn {
    background: #ff4757;
    border: none;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.stop-generating-btn:hover {
    background: #ff3742;
    transform: scale(1.05);
}

.stop-generating-btn .material-symbols-outlined {
    font-size: 16px;
}

/* AI输入区域 */
.ai-chat-input-area {
    padding: 16px;
    border-top: 1px solid var(--border-color);
    background: #fafbfc;
    border-radius: 0 0 12px 12px;
    /* 确保输入区域稳定 */
    position: relative;
    flex-shrink: 0; /* 防止被压缩 */
}

.ai-input-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.ai-input {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    font-family: var(--font-family);
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    outline: none;
    transition: border-color 0.2s ease;
    background: white;
    /* 确保输入框稳定 */
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.ai-input:focus {
    border-color: var(--primary-color);
}

.ai-input:disabled {
    background: #f5f5f5;
    color: var(--text-light);
    cursor: not-allowed;
}

.ai-send-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.ai-send-btn:hover:not(:disabled) {
    background: #2a6ad9;
    transform: scale(1.05);
}

.ai-send-btn:disabled {
    background: var(--text-lighter);
    cursor: not-allowed;
    transform: none;
}

.ai-send-btn .material-symbols-outlined {
    font-size: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ai-chat-panel {
        width: calc(100vw - 40px);
        height: 60vh;
        right: 20px;
        left: 20px;
        bottom: 80px;
    }

    .ai-float-btn {
        bottom: 20px;
        right: 20px;
    }

    .message-bubble {
        max-width: 85%;
    }

    .ai-chat-header {
        padding: 12px 16px;
    }

    .ai-chat-header h3 {
        font-size: 14px;
    }

    .ai-chat-messages {
        padding: 12px;
    }

    .ai-chat-input-area {
        padding: 12px;
    }
}

@media (max-width: 480px) {
    .ai-chat-panel {
        width: calc(100vw - 20px);
        height: 70vh;
        right: 10px;
        left: 10px;
        bottom: 70px;
    }

    .message-bubble {
        max-width: 90%;
        padding: 10px 14px;
    }

    .message-text {
        font-size: 13px;
    }
}

/* 选择工具栏 */
.selection-toolbar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border-color);
    display: flex;
    gap: 2px;
    padding: 6px;
    animation: toolbarSlideIn 0.2s ease-out;
    backdrop-filter: blur(10px);
    max-width: 300px;
}

@keyframes toolbarSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.selection-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 10px;
    background: none;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    color: var(--text-color);
    transition: all 0.2s ease;
    white-space: nowrap;
    font-weight: 500;
    position: relative;
}

.selection-btn:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.selection-btn:active {
    transform: translateY(0);
}

.selection-btn .material-symbols-outlined {
    font-size: 14px;
    font-variation-settings: 'FILL' 0;
}

.selection-btn:hover .material-symbols-outlined {
    font-variation-settings: 'FILL' 1;
}

/* 右键菜单 */
.context-menu {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--border-color);
    padding: 8px 0;
    min-width: 160px;
    animation: menuSlideIn 0.2s ease-out;
}

@keyframes menuSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.context-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-color);
    transition: background-color 0.2s ease;
}

.context-menu-item:hover {
    background: var(--hover-bg);
    color: var(--primary-color);
}

.context-menu-item .material-symbols-outlined {
    font-size: 18px;
    color: var(--text-light);
}

.context-menu-item:hover .material-symbols-outlined {
    color: var(--primary-color);
}

.context-menu-separator {
    height: 1px;
    background: var(--border-color);
    margin: 4px 0;
}

/* 滚动条样式 */
.ai-chat-messages::-webkit-scrollbar {
    width: 6px;
}

.ai-chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.ai-chat-messages::-webkit-scrollbar-thumb {
    background: var(--text-lighter);
    border-radius: 3px;
}

.ai-chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-light);
}

/* === 编辑器工具栏优化 === */

/* 确保工具栏在任何情况下都保持正确的布局 */
.editor-toolbar {
    /* 强制工具栏保持固定布局 */
    position: sticky;
    top: 0;
    z-index: 10;
}

/* 工具栏按钮组合的优化 */
.toolbar-dropdown .dropdown-toggle {
    min-width: auto; /* 允许下拉按钮根据内容调整宽度 */
    max-width: 120px; /* 但不要过宽 */
}

.toolbar-dropdown .dropdown-label {
    flex-shrink: 0; /* 防止标签文字被压缩 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 响应式设计：在较小屏幕上优化工具栏 */
@media (max-width: 768px) {
    .editor-toolbar {
        padding: 2px 4px; /* 减少内边距 */
        gap: 1px; /* 减少按钮间距 */
    }

    .toolbar-btn {
        padding: 2px 4px; /* 减少按钮内边距 */
        min-width: 24px; /* 减少最小宽度 */
        height: 24px; /* 减少高度 */
        font-size: 12px; /* 减少字体大小 */
    }

    .toolbar-separator {
        margin: 0 2px; /* 减少分隔符边距 */
    }
}

/* 确保编辑器面板在各种布局模式下都能正确显示 */
.app-container .editor-panel {
    min-width: 300px; /* 设置最小宽度，防止过度压缩 */
}

/* ===== Google搜索功能样式 ===== */

/* 搜索信息容器 */
.search-info {
    margin-top: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #4285f4;
    font-size: 13px;
}

/* 搜索查询显示 */
.search-queries {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.search-icon {
    font-size: 14px;
}

.search-label {
    color: #5f6368;
    font-weight: 500;
}

.search-query {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 搜索来源显示 */
.search-sources {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.sources-label {
    color: #5f6368;
    font-weight: 500;
    flex-shrink: 0;
}

.sources-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.search-source {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: white;
    border: 1px solid #dadce0;
    border-radius: 16px;
    text-decoration: none;
    color: #1a73e8;
    font-size: 12px;
    transition: all 0.2s ease;
}

.search-source:hover {
    background: #e8f0fe;
    border-color: #4285f4;
    transform: translateY(-1px);
}

.source-number {
    background: #4285f4;
    color: white;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
}

.source-domain {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 引用链接样式 */
.citation-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    background: #4285f4;
    color: white;
    text-decoration: none;
    border-radius: 50%;
    font-size: 11px;
    font-weight: 600;
    margin: 0 2px;
    transition: all 0.2s ease;
    vertical-align: middle;
}

.citation-link:hover {
    background: #1a73e8;
    transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-info {
        padding: 8px;
        font-size: 12px;
    }

    .search-queries {
        flex-wrap: wrap;
    }

    .sources-list {
        flex-direction: column;
        gap: 4px;
    }

    .search-source {
        font-size: 11px;
        padding: 3px 6px;
    }

    .source-domain {
        max-width: 100px;
    }
}

/* 工具栏滚动条样式优化 */
.editor-toolbar::-webkit-scrollbar {
    height: 4px;
}

.editor-toolbar::-webkit-scrollbar-track {
    background: transparent;
}

.editor-toolbar::-webkit-scrollbar-thumb {
    background: var(--text-lighter);
    border-radius: 2px;
}

.editor-toolbar::-webkit-scrollbar-thumb:hover {
    background: var(--text-light);
}