<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机端登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .debug-info {
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 20px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>手机端登录测试</h2>
        
        <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" id="username" value="admin" placeholder="请输入用户名">
        </div>
        
        <div class="form-group">
            <label for="password">密码</label>
            <input type="password" id="password" value="" placeholder="请输入密码">
        </div>
        
        <button onclick="testConnection()">测试连接</button>
        <button onclick="testLogin()">测试登录</button>
        
        <div id="result"></div>
        
        <div class="debug-info">
            <strong>调试信息:</strong><br>
            <span id="debug-info"></span>
        </div>
    </div>

    <script>
        // 获取API基础URL
        function getApiBaseUrl() {
            const hostname = window.location.hostname;
            const protocol = window.location.protocol;
            
            const isDevelopment = hostname === 'localhost' || hostname === '127.0.0.1';
            const isLAN = hostname.startsWith('192.168.') || 
                          hostname.startsWith('10.') || 
                          hostname.startsWith('172.');
            
            if (isDevelopment) {
                return 'http://localhost:3001/api';
            } else if (isLAN) {
                return `${protocol}//${hostname}:3001/api`;
            } else {
                return `${protocol}//${window.location.host}/api`;
            }
        }

        // 显示调试信息
        function showDebugInfo() {
            const info = {
                'URL': window.location.href,
                'Hostname': window.location.hostname,
                'Protocol': window.location.protocol,
                'User Agent': navigator.userAgent.substring(0, 100) + '...',
                'API Base URL': getApiBaseUrl()
            };
            
            let debugText = '';
            for (const [key, value] of Object.entries(info)) {
                debugText += `${key}: ${value}<br>`;
            }
            
            document.getElementById('debug-info').innerHTML = debugText;
        }

        // 显示结果
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        // 测试连接
        async function testConnection() {
            const baseUrl = getApiBaseUrl();
            const testUrl = baseUrl.replace('/api', '');
            
            showResult('正在测试连接...', 'info');
            
            try {
                const response = await fetch(testUrl, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.text();
                    showResult(`✓ 连接成功！服务器响应: ${data}`, 'success');
                } else {
                    showResult(`✗ 连接失败！HTTP状态: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(`✗ 连接异常: ${error.message}`, 'error');
            }
        }

        // 测试登录
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'error');
                return;
            }
            
            const baseUrl = getApiBaseUrl();
            const loginUrl = `${baseUrl}/auth/login`;
            
            showResult('正在测试登录...', 'info');
            
            try {
                const response = await fetch(loginUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult(`✓ 登录成功！用户: ${data.user.username}`, 'success');
                } else {
                    const errorData = await response.json();
                    showResult(`✗ 登录失败: ${errorData.message}`, 'error');
                }
            } catch (error) {
                showResult(`✗ 登录异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示调试信息
        window.onload = function() {
            showDebugInfo();
        };
    </script>
</body>
</html>
