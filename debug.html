<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            border: 1px solid #ddd;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>笔记应用网络调试</h1>
    
    <div class="test-section">
        <h3>环境信息</h3>
        <div id="env-info" class="result info"></div>
    </div>
    
    <div class="test-section">
        <h3>API连接测试</h3>
        <button onclick="testApiConnection()">测试API连接</button>
        <button onclick="testLogin()">测试登录</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>网络诊断</h3>
        <button onclick="testNetworkDiagnostics()">网络诊断</button>
        <div id="network-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>调试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="log"></div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 显示环境信息
        function showEnvironmentInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Current URL': window.location.href,
                'Hostname': window.location.hostname,
                'Port': window.location.port,
                'Protocol': window.location.protocol,
                'Is Mobile': /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
            };
            
            let html = '';
            for (const [key, value] of Object.entries(info)) {
                html += `<strong>${key}:</strong> ${value}<br>`;
            }
            
            document.getElementById('env-info').innerHTML = html;
            log('环境信息已显示');
        }

        // 动态获取API基础URL
        function getApiBaseUrl() {
            const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            
            if (isDevelopment) {
                return 'http://localhost:3001/api';
            } else {
                return `${window.location.protocol}//${window.location.host}/api`;
            }
        }

        // 测试API连接
        async function testApiConnection() {
            const resultDiv = document.getElementById('api-result');
            const baseUrl = getApiBaseUrl();
            
            log(`开始测试API连接: ${baseUrl}`);
            
            try {
                // 测试根路径
                const rootResponse = await fetch(baseUrl.replace('/api', ''));
                log(`根路径响应状态: ${rootResponse.status}`);
                
                if (rootResponse.ok) {
                    const rootData = await rootResponse.text();
                    log(`根路径响应: ${rootData}`);
                    resultDiv.innerHTML = `<div class="success">✓ 服务器连接成功</div>`;
                } else {
                    throw new Error(`服务器响应错误: ${rootResponse.status}`);
                }
            } catch (error) {
                log(`API连接失败: ${error.message}`, 'error');
                resultDiv.innerHTML = `<div class="error">✗ API连接失败: ${error.message}</div>`;
            }
        }

        // 测试登录
        async function testLogin() {
            const resultDiv = document.getElementById('api-result');
            const baseUrl = getApiBaseUrl();
            const loginUrl = `${baseUrl}/auth/login`;
            
            log(`开始测试登录: ${loginUrl}`);
            
            try {
                const response = await fetch(loginUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'aaaaa'
                    }),
                });

                log(`登录响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`登录成功: ${data.user.username}`);
                    resultDiv.innerHTML = `<div class="success">✓ 登录测试成功</div>`;
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '登录失败');
                }
            } catch (error) {
                log(`登录测试失败: ${error.message}`, 'error');
                resultDiv.innerHTML = `<div class="error">✗ 登录测试失败: ${error.message}</div>`;
            }
        }

        // 网络诊断
        async function testNetworkDiagnostics() {
            const resultDiv = document.getElementById('network-result');
            const baseUrl = getApiBaseUrl();
            
            log('开始网络诊断');
            
            const tests = [
                {
                    name: '本地服务器连接',
                    url: 'http://localhost:3001/',
                },
                {
                    name: 'API端点连接',
                    url: baseUrl,
                },
                {
                    name: '外部网络连接',
                    url: 'https://httpbin.org/get',
                }
            ];

            let results = [];
            
            for (const test of tests) {
                try {
                    log(`测试: ${test.name} - ${test.url}`);
                    const startTime = Date.now();
                    const response = await fetch(test.url, { 
                        method: 'GET',
                        mode: 'cors'
                    });
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    if (response.ok) {
                        results.push(`✓ ${test.name}: 成功 (${duration}ms)`);
                        log(`${test.name}: 成功 (${duration}ms)`, 'success');
                    } else {
                        results.push(`✗ ${test.name}: HTTP ${response.status}`);
                        log(`${test.name}: HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    results.push(`✗ ${test.name}: ${error.message}`);
                    log(`${test.name}: ${error.message}`, 'error');
                }
            }
            
            resultDiv.innerHTML = results.map(r => 
                `<div class="${r.startsWith('✓') ? 'success' : 'error'}">${r}</div>`
            ).join('');
        }

        // 页面加载时显示环境信息
        window.onload = function() {
            showEnvironmentInfo();
            log('调试页面已加载');
        };
    </script>
</body>
</html>
