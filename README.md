# Notes Application - 笔记应用 (Vite + TypeScript + MySQL)

一个功能完整的全栈笔记应用，采用现代化的技术栈构建，支持富文本编辑、层级文件夹管理和安全的用户认证系统。

## 🚀 功能特点

### 核心功能
- **用户认证系统**: JWT-based 安全认证，支持登录/登出
- **层级文件夹管理**: 支持无限层级的文件夹创建、重命名、删除
- **富文本笔记编辑**: 完整的WYSIWYG编辑器，支持格式化、列表、链接、图片、表格等
- **实时自动保存**: 1秒防抖自动保存，避免数据丢失
- **响应式设计**: 适配桌面和移动设备
- **智能侧边栏**: 三种模式（完全展开/部分收缩/最小化）
- **AI智能助手**: Google Gemini驱动的AI对话，支持流式输出和内容插入

### 高级特性
- **优化的用户体验**: 乐观更新、加载状态、错误处理
- **数据安全**: 用户数据隔离、密码加密存储
- **性能优化**: 数据库连接池、防抖处理、懒加载
- **可扩展架构**: 模块化设计，易于扩展新功能
- **AI智能助手**: 集成Google Gemini AI，支持实时对话和内容生成

## 🏗️ 系统架构

### 整体架构
```
┌─────────────────┐    HTTP/REST API    ┌─────────────────┐
│   Frontend      │ ◄─────────────────► │   Backend       │
│   (TypeScript)  │                     │   (Node.js)     │
└─────────────────┘                     └─────────────────┘
         │                                        │
         │                                        │
         ▼                                        ▼
┌─────────────────┐                     ┌─────────────────┐
│  Browser APIs   │                     │   MySQL DB      │
│  LocalStorage   │                     │  Connection     │
└─────────────────┘                     │     Pool        │
                                        └─────────────────┘
```

### 技术栈详解

#### 前端技术栈
- **TypeScript 5.7.2**: 类型安全的JavaScript超集
- **Vite 6.2.0**: 现代化构建工具，支持热重载
- **原生DOM操作**: 无框架依赖，轻量级实现
- **CSS3**: 现代CSS特性，支持响应式设计
- **Material Symbols**: Google图标字体

#### 后端技术栈
- **Node.js**: JavaScript运行时环境
- **Express 4.18.2**: Web应用框架
- **MySQL2 3.6.1**: MySQL数据库驱动，支持Promise
- **JWT (jsonwebtoken 9.0.2)**: 无状态身份认证
- **bcrypt 5.1.1**: 密码加密哈希
- **CORS 2.8.5**: 跨域资源共享配置

#### 开发工具
- **nodemon**: 开发时自动重启服务器
- **dotenv**: 环境变量管理

## 📁 项目结构

```
notes/
├── 📂 Frontend (根目录)
│   ├── index.html              # 主HTML入口文件
│   ├── index.tsx               # 主TypeScript应用 (2500+ 行)
│   ├── index.css               # 完整样式表 (2000+ 行)
│   ├── api.ts                  # 前端API服务层
│   ├── package.json            # 前端依赖配置
│   ├── tsconfig.json           # TypeScript配置
│   ├── vite.config.ts          # Vite构建配置
│   └── metadata.json           # 应用元数据
├── 📂 Backend (bankend/)
│   ├── server.js               # Express服务器入口
│   ├── db.js                   # MySQL数据库连接池
│   ├── package.json            # 后端依赖配置
│   ├── create_users_table.sql  # 数据库表结构
│   ├── 📂 middleware/
│   │   └── auth.js             # JWT认证中间件
│   └── 📂 routes/
│       ├── auth.js             # 认证路由 (登录/验证)
│       ├── notes.js            # 笔记CRUD路由
│       └── folders.js          # 文件夹CRUD路由
└── 📂 jietu/                   # 截图目录 (空)
```

## 🔧 核心组件详解

### 前端架构

#### 1. 主应用类 (`NotesApp`)
- **状态管理**: 集中式状态管理，使用不可变更新模式
- **事件处理**: 方法绑定和集中事件委托
- **生命周期**: 完整的组件生命周期管理

```typescript
interface AppState {
    user: User | null;                    // 用户信息
    folders: Folder[];                    // 文件夹列表
    notes: Note[];                        // 笔记列表
    activeFolderId: string | null;        // 当前选中文件夹
    activeNoteId: string | null;          // 当前选中笔记
    isFolderView: boolean;                // 是否为文件夹视图
    editingItemId: string | null;         // 正在编辑的项目ID
    sidebarCollapseMode: string;          // 侧边栏模式
    isLoading: boolean;                   // 加载状态
    errorMessage: string | null;          // 错误信息
    controlsVisible: boolean;             // 控制面板可见性
}
```

#### 2. API服务层 (`ApiService`)
- **HTTP客户端**: 基于Fetch API的RESTful客户端
- **认证管理**: 自动JWT token处理和刷新
- **错误处理**: 统一的错误处理和用户友好的错误信息

#### 3. 富文本编辑器
- **实现方式**: 基于`contentEditable`和`document.execCommand`
- **功能特性**:
  - 文本格式化 (粗体、斜体、下划线、删除线)
  - 字体和字号选择
  - 文字和背景颜色
  - 列表 (有序/无序)
  - 链接插入和编辑
  - 图片上传和插入
  - 表格创建
  - 代码块
  - 待办事项
- **自动保存**: 1秒防抖自动保存机制

#### 4. AI智能助手
<augment_code_snippet path="index.tsx" mode="EXCERPT">
```typescript
// AI服务集成
class GeminiAIService {
    async sendMessageStream(chat: any, message: string, onChunk: (text: string) => void)
    createChat(history: Array<{role: 'user' | 'model', parts: Array<{text: string}>}>)
}
```
</augment_code_snippet>

**AI功能特性**:
- Google Gemini 2.5 Flash模型驱动
- 实时流式输出，逐字显示AI回复
- 智能对话历史管理
- 一键插入AI内容到笔记
- 支持Markdown格式渲染
- 快捷键支持 (Ctrl/Cmd + K)

### 后端架构

#### 1. Express服务器 (`server.js`)
- **中间件配置**: CORS、JSON解析、错误处理
- **路由挂载**: 模块化路由管理
- **安全配置**: 请求大小限制、CORS策略

#### 2. 数据库层 (`db.js`)
- **连接池**: MySQL2连接池，支持并发连接
- **配置管理**: 环境变量驱动的数据库配置
- **健康检查**: 启动时数据库连接测试

#### 3. 认证系统
- **JWT认证**: 无状态token认证
- **密码安全**: bcrypt哈希加密
- **中间件保护**: 路由级别的认证保护

## 🚀 安装和运行

### 环境要求
- Node.js 16.0+
- MySQL 8.0+
- 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)

### 数据库设置

1. **启动MySQL服务**
2. **创建数据库**:
```sql
CREATE DATABASE notes CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE notes;
```

3. **创建表结构**:
```bash
# 在MySQL中执行
source bankend/create_users_table.sql;
```

### 后端设置

1. **进入后端目录**:
```bash
cd bankend
```

2. **安装依赖**:
```bash
npm install
```

3. **配置环境变量** (创建 `.env` 文件):
```env
DB_HOST=localhost
DB_USER=your_mysql_user
DB_PASSWORD=your_mysql_password
DB_DATABASE=notes
JWT_SECRET=your_jwt_secret_key
PORT=3001
```

4. **启动后端服务**:
```bash
# 生产模式
npm start

# 开发模式 (自动重启)
npm run dev
```

### 前端设置

1. **安装前端依赖**:
```bash
npm install
```

2. **配置AI功能** (可选):
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，添加你的Gemini API密钥
# GEMINI_API_KEY=your_actual_api_key_here
```

获取Gemini API密钥：
- 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
- 登录Google账户并创建API密钥
- 将密钥添加到`.env`文件中

3. **启动开发服务器**:
```bash
# 使用Vite开发服务器
npm run dev

# 或使用其他HTTP服务器
npx http-server . -p 8080
```

4. **访问应用**: `http://localhost:5173` (Vite) 或 `http://localhost:8080`

### 生产部署

1. **构建前端**:
```bash
npm run build
```

2. **部署静态文件**: 将构建产物部署到Web服务器
3. **配置反向代理**: 将API请求代理到后端服务器

## 🔐 默认账户


> 注意: 生产环境中请务必修改默认密码

## 📡 API接口文档

### 认证接口 (`/api/auth`)

#### `POST /api/auth/login`
用户登录认证
```json
// 请求体

// 响应
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "admin"
  }
}
```

#### `GET /api/auth/verify`
验证JWT Token有效性
```http
Authorization: Bearer <token>
```

#### `POST /api/auth/register`
用户注册 (可选功能)
```json
{
  "username": "newuser",
  "password": "password123"
}
```

### 文件夹接口 (`/api/folders`)

#### `GET /api/folders`
获取用户所有文件夹
```json
// 响应
[
  {
    "id": 1,
    "name": "工作笔记",
    "parentId": null,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
]
```

#### `POST /api/folders`
创建新文件夹
```json
// 请求体
{
  "name": "新文件夹",
  "parentId": 1  // 可选，父文件夹ID
}
```

#### `PUT /api/folders/:id`
更新文件夹信息
```json
{
  "name": "更新后的文件夹名",
  "parentId": 2
}
```

#### `DELETE /api/folders/:id`
删除文件夹 (级联删除子文件夹和笔记)

### 笔记接口 (`/api/notes`)

#### `GET /api/notes`
获取用户所有笔记
```http
# 获取所有笔记
GET /api/notes

# 获取指定文件夹中的笔记
GET /api/notes?folderId=1
```

```json
// 响应
[
  {
    "id": 1,
    "folderId": 1,
    "title": "我的第一篇笔记",
    "content": "<div>笔记内容...</div>",
    "size": "1.2 KB",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
]
```

#### `GET /api/notes/:id`
获取单个笔记详情

#### `POST /api/notes`
创建新笔记
```json
{
  "title": "新笔记标题",
  "content": "<div>笔记内容</div>",
  "folderId": 1  // 可选
}
```

#### `PUT /api/notes/:id`
更新笔记
```json
{
  "title": "更新后的标题",
  "content": "<div>更新后的内容</div>",
  "folderId": 2
}
```

#### `DELETE /api/notes/:id`
删除笔记

### 错误响应格式
```json
{
  "message": "错误描述信息"
}
```

常见HTTP状态码:
- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 🎨 设计模式和架构决策

### 前端设计模式

1. **单例模式**: API服务类使用单例模式，确保全局唯一实例
2. **观察者模式**: 状态变更触发UI重新渲染
3. **命令模式**: 富文本编辑器的工具栏操作
4. **策略模式**: 不同的侧边栏显示模式

### 后端设计模式

1. **MVC模式**: 路由(Controller) + 数据库操作(Model) + JSON响应(View)
2. **中间件模式**: Express中间件链式处理请求
3. **工厂模式**: 数据库连接池的创建和管理
4. **装饰器模式**: JWT认证中间件装饰路由

### 架构优势

- **模块化**: 前后端分离，组件化开发
- **可扩展性**: 易于添加新功能和集成第三方服务
- **可维护性**: 清晰的代码结构和职责分离
- **性能优化**: 连接池、防抖、乐观更新等优化策略
- **安全性**: JWT认证、密码加密、用户数据隔离

## 🔧 开发指南

### 添加新功能

1. **前端新功能**:
   - 在`NotesApp`类中添加新的状态字段
   - 实现对应的事件处理方法
   - 更新渲染逻辑
   - 在API服务中添加新的接口调用

2. **后端新功能**:
   - 在对应的路由文件中添加新的端点
   - 实现数据库操作逻辑
   - 添加必要的验证和错误处理
   - 更新API文档

### 代码规范

- **TypeScript**: 严格类型检查，使用接口定义数据结构
- **命名规范**: 驼峰命名法，语义化命名
- **错误处理**: 统一的错误处理机制
- **注释**: 关键逻辑添加中文注释

### 测试建议

1. **单元测试**: 对核心业务逻辑进行单元测试
2. **集成测试**: 测试API端点的完整流程
3. **E2E测试**: 使用Playwright或Cypress进行端到端测试
4. **性能测试**: 对数据库查询和API响应时间进行性能测试

## 🤖 AI智能助手功能

### 功能概述
本应用集成了Google Gemini AI，为用户提供智能写作和内容生成助手。

### 主要特性

#### 1. 智能对话
- **实时流式输出**: AI回复逐字显示，提供流畅的对话体验
- **上下文理解**: 支持多轮对话，AI能理解对话历史
- **智能回复**: 基于Gemini 2.5 Flash模型，提供高质量的回复

#### 2. 内容操作
- **一键插入**: 将AI生成的内容直接插入到当前笔记
- **格式保持**: 支持Markdown格式，保持文本结构
- **智能定位**: 在光标位置插入内容，或追加到笔记末尾

#### 3. 用户界面
- **悬浮按钮**: 编辑器右下角的AI助手入口
- **对话面板**: 优雅的对话界面，支持消息历史
- **快捷操作**: 复制、插入、清空对话等便捷功能

#### 4. 快捷键支持
- **Ctrl/Cmd + K**: 快速打开/关闭AI助手
- **Enter**: 发送消息
- **Shift + Enter**: 换行
- **Escape**: 关闭AI面板

### 使用场景
- **写作助手**: 帮助完善文章内容和结构
- **创意生成**: 提供创意想法和灵感
- **问题解答**: 回答各种问题和疑惑
- **内容优化**: 改进和润色现有文本

### 配置要求
- 需要有效的Google Gemini API密钥
- 网络连接以访问AI服务
- 现代浏览器支持

## 🚀 未来改进方向

### 功能增强
- [ ] 笔记标签系统
- [ ] 全文搜索功能
- [ ] 笔记分享和协作
- [ ] 导入/导出功能 (Markdown, PDF)
- [ ] 主题切换 (暗色模式)
- [ ] 移动端适配优化
- [ ] 离线支持 (PWA)

### 技术优化
- [ ] 前端框架迁移 (React/Vue)
- [ ] 数据库迁移 (PostgreSQL)
- [ ] 缓存层 (Redis)
- [ ] 文件存储 (云存储)
- [ ] 容器化部署 (Docker)
- [ ] CI/CD流水线
- [ ] 监控和日志系统

### AI集成
- [ ] 智能笔记摘要
- [ ] 内容推荐
- [ ] 语法检查和改进建议
- [ ] 自动标签生成

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📞 联系方式

如有问题或建议，请通过以下方式联系:

- 提交 [GitHub Issue](https://github.com/your-repo/issues)
- 发送邮件至: <EMAIL>

---

**感谢使用 Notes Application! 🎉**
