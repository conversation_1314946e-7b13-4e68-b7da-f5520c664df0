# AI智能助手功能使用指南

## 🤖 功能概述

本笔记应用集成了Google Gemini AI，为用户提供智能写作和内容生成助手。AI助手可以帮助你：

- 回答各种问题
- 协助写作和编辑
- 生成创意内容
- 解释复杂概念
- 优化现有文本

## 🚀 快速开始

### 1. 配置API密钥

首先需要获取Google Gemini API密钥：

1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 登录你的Google账户
3. 创建新的API密钥
4. 复制API密钥

然后配置到应用中：

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，将your_gemini_api_key_here替换为你的实际API密钥
GEMINI_API_KEY=your_actual_api_key_here
```

### 2. 启动应用

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 3. 使用AI助手

1. 打开笔记应用
2. 创建或打开一篇笔记
3. 点击编辑器右下角的AI助手按钮（🤖图标）
4. 在对话框中输入你的问题或需求
5. 按Enter发送消息，AI会实时回复

## 💡 使用技巧

### 快捷键

- **Ctrl/Cmd + K**: 快速打开/关闭AI助手
- **Enter**: 发送消息
- **Shift + Enter**: 在输入框中换行
- **Escape**: 关闭AI对话面板

### 消息操作

每条AI回复都提供以下操作：

- **插入按钮** (📦): 将AI回复内容插入到当前笔记的光标位置
- **复制按钮** (📋): 复制AI回复内容到剪贴板

### 对话管理

- **清空对话**: 点击对话框顶部的删除按钮清空历史记录
- **关闭对话**: 点击对话框顶部的关闭按钮或按Escape键

## 🎯 使用场景示例

### 1. 写作助手

**用户**: "帮我写一篇关于人工智能发展历程的文章大纲"

**AI**: 会生成详细的文章大纲，你可以直接插入到笔记中作为写作框架。

### 2. 内容优化

**用户**: "请帮我优化这段文字：[粘贴你的文字]"

**AI**: 会提供改进建议和优化后的版本。

### 3. 问题解答

**用户**: "什么是RESTful API？"

**AI**: 会提供详细的解释和示例。

### 4. 创意生成

**用户**: "给我一些关于环保主题的创意想法"

**AI**: 会提供多个创意方向和具体建议。

## ⚠️ 注意事项

1. **API密钥安全**: 不要将包含真实API密钥的.env文件提交到版本控制系统
2. **网络连接**: AI功能需要稳定的网络连接
3. **API配额**: Google Gemini API有使用配额限制，请合理使用
4. **内容准确性**: AI生成的内容仅供参考，请根据实际情况判断和使用

## 🔧 故障排除

### AI助手无法使用

1. 检查API密钥是否正确配置
2. 确认网络连接正常
3. 查看浏览器控制台是否有错误信息

### 流式输出异常

1. 刷新页面重试
2. 检查网络连接稳定性
3. 确认API密钥有效且未超出配额

### 内容插入失败

1. 确保笔记编辑器处于活动状态
2. 尝试点击编辑器区域后再插入
3. 检查笔记是否已保存

## 📞 技术支持

如遇到问题，请：

1. 查看浏览器控制台的错误信息
2. 检查网络连接和API配置
3. 参考项目README.md中的详细说明

---

**享受AI助手带来的智能写作体验！** 🎉
