const jwt = require('jsonwebtoken');
require('dotenv').config();

// 验证JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ message: '未提供认证token' });
  }
  
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'token无效或已过期' });
    }
    
    req.user = user;
    next();
  });
};

module.exports = {
  authenticateToken
}; 