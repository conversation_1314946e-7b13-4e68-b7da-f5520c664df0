const express = require('express');
const cors = require('cors');
require('dotenv').config();
const { testConnection } = require('./db');

// 导入路由
const authRoutes = require('./routes/auth');
const folderRoutes = require('./routes/folders');
const noteRoutes = require('./routes/notes');

// 初始化应用
const app = express();
const PORT = process.env.PORT || 3001;

// 中间件: 配置 CORS，允许跨域携带 Authorization 头
const corsOptions = {
  origin: true,           // 允许所有源或可以指定具体域名
  methods: ['GET','POST','PUT','PATCH','DELETE','OPTIONS'],
  allowedHeaders: ['Content-Type','Authorization'],
  credentials: true
};
app.use(cors(corsOptions));
// 对所有预检请求应用 CORS
app.options('*', cors(corsOptions));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 测试数据库连接
testConnection();

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/folders', folderRoutes);
app.use('/api/notes', noteRoutes);

// 根路由
app.get('/', (req, res) => {
  res.json({ message: '欢迎使用笔记应用API' });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: '服务器错误', error: process.env.NODE_ENV === 'development' ? err.message : undefined });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
}); 