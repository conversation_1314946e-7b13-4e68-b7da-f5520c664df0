# 前端环境变量配置文件
# 复制此文件到项目根目录的 .env 文件

# Gemini AI API Configuration
# 请在这里设置你的 Google Gemini API 密钥
# 获取API密钥: https://aistudio.google.com/app/apikey

GEMINI_API_KEY=your_gemini_api_key_here
API_KEY=your_gemini_api_key_here

# 注意：
# 1. 请将 your_gemini_api_key_here 替换为你的实际API密钥
# 2. 不要将包含真实API密钥的.env文件提交到版本控制系统
# 3. 确保将.env文件添加到.gitignore中
# 4. API密钥在构建时会被注入到前端代码中，请妥善保管

# 如何获取API密钥：
# 1. 访问 https://aistudio.google.com/app/apikey
# 2. 登录你的Google账户
# 3. 创建新的API密钥
# 4. 将密钥复制到上面的配置中

# 安全提示：
# - 定期轮换API密钥
# - 监控API使用量
# - 设置适当的配额限制
