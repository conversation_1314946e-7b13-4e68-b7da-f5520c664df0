# 后端环境变量配置文件
# 复制此文件到 bankend/.env 并修改相应的值

# 服务器配置
PORT=3001
NODE_ENV=production

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=notes_db
DB_USER=notes_user
DB_PASSWORD=your_secure_password_here

# JWT 配置
JWT_SECRET=your_jwt_secret_key_here_make_it_very_long_and_random
JWT_EXPIRES_IN=7d

# CORS 配置
CORS_ORIGIN=http://your-domain.com

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/log/notes/backend.log

# 文件上传配置
MAX_FILE_SIZE=50mb
UPLOAD_DIR=/var/www/notes/uploads
