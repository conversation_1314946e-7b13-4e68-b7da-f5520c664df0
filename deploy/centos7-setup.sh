#!/bin/bash

# CentOS 7 服务器环境准备脚本
# 用于部署 Notes 应用的前后端

set -e

echo "=== CentOS 7 环境准备开始 ==="

# 更新系统
echo "1. 更新系统包..."
sudo yum update -y

# 安装基础工具
echo "2. 安装基础工具..."
sudo yum install -y wget curl git vim unzip

# 安装 Node.js (使用 NodeSource 仓库安装 Node.js 18)
echo "3. 安装 Node.js..."
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证 Node.js 安装
node_version=$(node --version)
npm_version=$(npm --version)
echo "Node.js 版本: $node_version"
echo "npm 版本: $npm_version"

# 安装 PM2 全局包管理器
echo "4. 安装 PM2..."
sudo npm install -g pm2

# 安装 MySQL 5.7
echo "5. 安装 MySQL 5.7..."
# 下载 MySQL 5.7 仓库
wget https://dev.mysql.com/get/mysql57-community-release-el7-11.noarch.rpm
sudo rpm -ivh mysql57-community-release-el7-11.noarch.rpm
sudo yum install -y mysql-server

# 启动 MySQL 服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 获取 MySQL 临时密码
temp_password=$(sudo grep 'temporary password' /var/log/mysqld.log | awk '{print $NF}')
echo "MySQL 临时密码: $temp_password"
echo "请记录此密码，稍后需要用于初始化 MySQL"

# 安装 Nginx
echo "6. 安装 Nginx..."
sudo yum install -y epel-release
sudo yum install -y nginx

# 启动 Nginx 服务
sudo systemctl start nginx
sudo systemctl enable nginx

# 配置防火墙
echo "7. 配置防火墙..."
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 开放必要端口
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-port=3001/tcp  # 后端端口
sudo firewall-cmd --reload

# 创建应用目录
echo "8. 创建应用目录..."
sudo mkdir -p /var/www/notes
sudo chown -R $USER:$USER /var/www/notes

# 创建日志目录
sudo mkdir -p /var/log/notes
sudo chown -R $USER:$USER /var/log/notes

echo "=== 环境准备完成 ==="
echo ""
echo "下一步操作："
echo "1. 运行 mysql_secure_installation 来设置 MySQL root 密码"
echo "2. 创建应用数据库和用户"
echo "3. 部署应用代码"
echo ""
echo "MySQL 临时密码: $temp_password"
