#!/bin/bash

# 数据库配置脚本
# 用于初始化 MySQL 数据库和创建应用所需的表结构

set -e

echo "=== 数据库配置开始 ==="

# 检查 MySQL 是否运行
if ! systemctl is-active --quiet mysqld; then
    echo "启动 MySQL 服务..."
    sudo systemctl start mysqld
fi

# 提示用户设置 MySQL root 密码
echo "1. 首先需要设置 MySQL root 密码"
echo "请运行以下命令来安全配置 MySQL："
echo "sudo mysql_secure_installation"
echo ""
read -p "是否已经完成 MySQL 安全配置？(y/n): " mysql_configured

if [ "$mysql_configured" != "y" ]; then
    echo "请先运行 mysql_secure_installation 完成 MySQL 安全配置"
    exit 1
fi

# 获取数据库密码
echo ""
echo "2. 设置应用数据库密码"
read -s -p "请输入要为 notes_user 设置的密码: " db_password
echo ""
read -s -p "请再次确认密码: " db_password_confirm
echo ""

if [ "$db_password" != "$db_password_confirm" ]; then
    echo "密码不匹配，请重新运行脚本"
    exit 1
fi

# 创建临时 SQL 文件
temp_sql="/tmp/notes_setup.sql"
cat > "$temp_sql" << EOF
-- 创建数据库
CREATE DATABASE IF NOT EXISTS notes_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建应用用户
CREATE USER IF NOT EXISTS 'notes_user'@'localhost' IDENTIFIED BY '$db_password';

-- 授权
GRANT ALL PRIVILEGES ON notes_db.* TO 'notes_user'@'localhost';
FLUSH PRIVILEGES;

-- 使用数据库
USE notes_db;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建文件夹表
CREATE TABLE IF NOT EXISTS folders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    user_id INT NOT NULL,
    parent_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES folders(id) ON DELETE CASCADE
);

-- 创建笔记表
CREATE TABLE IF NOT EXISTS notes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content LONGTEXT,
    user_id INT NOT NULL,
    folder_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX idx_folders_user_id ON folders(user_id);
CREATE INDEX idx_folders_parent_id ON folders(parent_id);
CREATE INDEX idx_notes_user_id ON notes(user_id);
CREATE INDEX idx_notes_folder_id ON notes(folder_id);
CREATE INDEX idx_notes_created_at ON notes(created_at);

-- 显示创建的表
SHOW TABLES;
EOF

# 执行 SQL 脚本
echo "3. 创建数据库和表结构..."
read -s -p "请输入 MySQL root 密码: " root_password
echo ""

mysql -u root -p"$root_password" < "$temp_sql"

if [ $? -eq 0 ]; then
    echo "数据库配置成功！"
    
    # 更新环境变量文件
    if [ -f "deploy/backend.env" ]; then
        sed -i "s/DB_PASSWORD=your_secure_password_here/DB_PASSWORD=$db_password/" deploy/backend.env
        echo "已更新 deploy/backend.env 中的数据库密码"
    fi
    
    echo ""
    echo "数据库信息："
    echo "数据库名: notes_db"
    echo "用户名: notes_user"
    echo "密码: [已设置]"
    
else
    echo "数据库配置失败，请检查错误信息"
    exit 1
fi

# 清理临时文件
rm -f "$temp_sql"

echo "=== 数据库配置完成 ==="
