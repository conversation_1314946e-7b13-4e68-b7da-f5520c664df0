#!/bin/bash

# 后端部署脚本
# 用于部署 Node.js 后端应用

set -e

echo "=== 后端部署开始 ==="

# 检查当前目录
if [ ! -d "bankend" ]; then
    echo "错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 创建部署目录
DEPLOY_DIR="/var/www/notes/backend"
echo "1. 创建部署目录..."
sudo mkdir -p "$DEPLOY_DIR"
sudo chown -R $USER:$USER /var/www/notes

# 复制后端代码
echo "2. 复制后端代码..."
cp -r bankend/* "$DEPLOY_DIR/"

# 进入部署目录
cd "$DEPLOY_DIR"

# 安装依赖
echo "3. 安装后端依赖..."
npm install --production

# 配置环境变量
echo "4. 配置环境变量..."
if [ -f "../../../deploy/backend.env" ]; then
    cp "../../../deploy/backend.env" .env
    echo "已复制环境变量文件"
    
    # 提示用户修改配置
    echo ""
    echo "请编辑 .env 文件，修改以下配置："
    echo "- DB_PASSWORD: 数据库密码"
    echo "- JWT_SECRET: JWT 密钥（建议使用随机字符串）"
    echo "- CORS_ORIGIN: 前端域名"
    echo ""
    read -p "是否现在编辑 .env 文件？(y/n): " edit_env
    
    if [ "$edit_env" = "y" ]; then
        vim .env
    fi
else
    echo "警告: 未找到环境变量模板文件"
fi

# 创建 PM2 配置文件
echo "5. 创建 PM2 配置..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'notes-backend',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: '/var/log/notes/backend-error.log',
    out_file: '/var/log/notes/backend-out.log',
    log_file: '/var/log/notes/backend-combined.log',
    time: true
  }]
};
EOF

# 创建日志目录
sudo mkdir -p /var/log/notes
sudo chown -R $USER:$USER /var/log/notes

# 测试应用启动
echo "6. 测试应用..."
if node server.js &
then
    sleep 3
    if curl -f http://localhost:3001/health > /dev/null 2>&1; then
        echo "应用测试成功"
        pkill -f "node server.js"
    else
        echo "警告: 应用可能无法正常启动，请检查配置"
        pkill -f "node server.js" || true
    fi
else
    echo "错误: 应用启动失败"
    exit 1
fi

# 使用 PM2 启动应用
echo "7. 使用 PM2 启动应用..."
pm2 start ecosystem.config.js

# 保存 PM2 配置
pm2 save

# 设置 PM2 开机自启
pm2 startup
echo "请运行上面显示的命令来设置 PM2 开机自启"

# 显示应用状态
echo ""
echo "8. 应用状态："
pm2 status

echo ""
echo "=== 后端部署完成 ==="
echo ""
echo "有用的 PM2 命令："
echo "- pm2 status          # 查看应用状态"
echo "- pm2 logs            # 查看日志"
echo "- pm2 restart all     # 重启应用"
echo "- pm2 stop all        # 停止应用"
echo "- pm2 delete all      # 删除应用"
echo ""
echo "后端服务运行在: http://localhost:3001"
