# Notes 应用 AI 功能部署指南

本指南专门针对包含 Google Gemini AI 功能的 Notes 应用部署到 CentOS 7。

## 🤖 AI 功能说明

应用集成了 Google Gemini AI，提供以下功能：
- 智能对话助手
- 文本生成和编辑
- Google 搜索集成
- 流式响应显示

## 📋 部署前准备

### 1. 获取 Gemini API 密钥

1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 登录你的 Google 账户
3. 点击 "Create API Key"
4. 复制生成的 API 密钥（格式：AIza...）

### 2. 准备服务器环境

- CentOS 7.x 系统
- 至少 2GB RAM
- 稳定的互联网连接（需要访问 Google API）
- 具有 sudo 权限的用户

## 🚀 快速部署

### 1. 上传项目文件

```bash
# 上传到服务器
scp -r notes/ user@server:/home/<USER>/
cd notes
```

### 2. 配置 API 密钥

```bash
# 复制环境变量模板
cp deploy/frontend.env .env

# 编辑配置文件
vim .env
```

在 `.env` 文件中设置：
```env
GEMINI_API_KEY=你的实际API密钥
API_KEY=你的实际API密钥
```

### 3. 运行一键部署

```bash
chmod +x deploy/deploy.sh
./deploy/deploy.sh
```

选择 "1) 完整部署"，脚本会自动：
- 安装系统依赖
- 配置数据库
- 部署后端
- 构建和部署前端（包含 AI 配置）
- 配置系统服务

### 4. 验证 AI 配置

```bash
# 检查 AI 配置
chmod +x deploy/check-ai-config.sh
./deploy/check-ai-config.sh
```

## 🔧 手动配置步骤

如果需要单独配置 AI 功能：

### 1. 配置前端环境变量

```bash
# 在项目根目录创建 .env 文件
cat > .env << 'EOF'
GEMINI_API_KEY=你的API密钥
API_KEY=你的API密钥
EOF
```

### 2. 重新构建前端

```bash
# 安装依赖
npm install

# 构建项目（API 密钥会被注入）
npm run build

# 部署到服务器
./deploy/frontend-deploy.sh
```

### 3. 测试 AI 功能

访问应用后：
1. 登录系统
2. 创建或打开笔记
3. 点击右下角的 AI 助手按钮
4. 发送测试消息

## 🛠️ 故障排除

### 常见问题

#### 1. AI 按钮不显示或无响应

**可能原因：**
- API 密钥未配置或配置错误
- 构建时未正确注入环境变量

**解决方法：**
```bash
# 检查配置
./deploy/check-ai-config.sh

# 重新配置和构建
vim .env  # 确保 API 密钥正确
npm run build
./deploy/frontend-deploy.sh
```

#### 2. AI 响应错误或超时

**可能原因：**
- 网络连接问题
- API 配额超限
- API 密钥无效

**解决方法：**
```bash
# 测试网络连接
curl -I https://generativelanguage.googleapis.com

# 检查 API 密钥
./deploy/check-ai-config.sh

# 查看浏览器控制台错误信息
```

#### 3. 构建失败

**可能原因：**
- 环境变量格式错误
- Node.js 版本不兼容

**解决方法：**
```bash
# 检查 .env 文件格式
cat .env

# 确保没有多余的空格或引号
# 正确格式：GEMINI_API_KEY=AIza...

# 重新构建
rm -rf dist node_modules
npm install
npm run build
```

### 日志查看

```bash
# 前端构建日志
npm run build

# Nginx 错误日志
sudo tail -f /var/log/nginx/notes_error.log

# 浏览器控制台
# 按 F12 查看 Console 标签页
```

## 🔒 安全注意事项

### 1. API 密钥安全

- **不要**将 API 密钥提交到版本控制系统
- 定期轮换 API 密钥
- 监控 API 使用量和费用
- 设置适当的 API 配额限制

### 2. 网络安全

```bash
# 确保防火墙配置正确
sudo firewall-cmd --list-all

# 只开放必要端口
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 3. 数据隐私

- AI 对话内容会发送到 Google 服务器
- 确保用户了解数据处理政策
- 考虑实施数据保留政策

## 📊 监控和维护

### 1. 定期检查

```bash
# 运行系统监控
/var/www/notes/monitor.sh

# 检查 AI 配置状态
./deploy/check-ai-config.sh
```

### 2. API 使用监控

- 访问 [Google Cloud Console](https://console.cloud.google.com/)
- 查看 API 使用情况和配额
- 设置使用量警报

### 3. 更新维护

```bash
# 更新前端（保持 AI 配置）
/var/www/notes/update-frontend.sh

# 备份配置
cp /var/www/notes/frontend/.env ~/backup/frontend.env.backup
```

## 🆘 获取帮助

如果遇到问题：

1. **检查配置**：运行 `./deploy/check-ai-config.sh`
2. **查看日志**：检查系统和应用日志
3. **测试网络**：确保可以访问 Google API
4. **验证密钥**：确认 API 密钥有效且有足够配额

## 📝 配置文件位置

- 项目环境变量：`/path/to/project/.env`
- 部署环境变量：`/var/www/notes/frontend/.env`
- 前端构建文件：`/var/www/notes/frontend/dist/`
- Nginx 配置：`/etc/nginx/conf.d/notes.conf`

记住：API 密钥在构建时会被注入到前端代码中，因此每次更改密钥后都需要重新构建和部署前端。
