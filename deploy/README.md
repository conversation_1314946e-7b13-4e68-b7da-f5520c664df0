# Notes 应用 CentOS 7 部署指南

本指南将帮助您在 CentOS 7 服务器上部署 Notes 应用的前后端。

## 系统要求

- CentOS 7.x
- 至少 2GB RAM
- 至少 20GB 磁盘空间
- 具有 sudo 权限的非 root 用户
- 互联网连接

## 快速部署

### 1. 准备项目文件

```bash
# 上传项目文件到服务器
scp -r /path/to/notes user@server:/home/<USER>/

# 或者使用 git 克隆
git clone <your-repo-url> notes
cd notes
```

### 2. 运行一键部署脚本

```bash
chmod +x deploy/deploy.sh
./deploy/deploy.sh
```

选择 "1) 完整部署" 并按照提示操作。

## 手动部署步骤

如果需要分步骤部署，可以按以下顺序执行：

### 1. 环境准备

```bash
chmod +x deploy/centos7-setup.sh
./deploy/centos7-setup.sh
```

这将安装：
- Node.js 18
- MySQL 5.7
- Nginx
- PM2
- 基础工具

### 2. 数据库配置

```bash
# 首先设置 MySQL root 密码
sudo mysql_secure_installation

# 然后运行数据库配置脚本
chmod +x deploy/database-setup.sh
./deploy/database-setup.sh
```

### 3. 后端部署

```bash
chmod +x deploy/backend-deploy.sh
./deploy/backend-deploy.sh
```

### 4. 前端部署

```bash
chmod +x deploy/frontend-deploy.sh
./deploy/frontend-deploy.sh
```

### 5. 系统配置

```bash
chmod +x deploy/system-config.sh
./deploy/system-config.sh
```

## 配置文件说明

### 环境变量配置

#### 后端环境变量

编辑 `deploy/backend.env` 文件，修改以下配置：

```env
# 数据库配置
DB_PASSWORD=your_secure_password_here

# JWT 密钥（请使用随机字符串）
JWT_SECRET=your_jwt_secret_key_here_make_it_very_long_and_random

# 前端域名
CORS_ORIGIN=http://your-domain.com
```

#### 前端环境变量

编辑 `deploy/frontend.env` 文件，配置 AI 功能：

```env
# Gemini AI API 密钥
GEMINI_API_KEY=your_gemini_api_key_here
API_KEY=your_gemini_api_key_here
```

**获取 Gemini API 密钥：**
1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 登录 Google 账户
3. 创建新的 API 密钥
4. 将密钥复制到配置文件中

**注意：** API 密钥会在构建时注入到前端代码中，请确保：
- 不要将真实密钥提交到版本控制
- 定期轮换 API 密钥
- 监控 API 使用量

### Nginx 配置

编辑 `deploy/nginx.conf` 文件，修改域名：

```nginx
server_name your-domain.com www.your-domain.com;
```

## 部署后配置

### 1. 设置 PM2 开机自启动

```bash
pm2 startup
# 运行显示的命令
pm2 save
```

### 2. 设置定时任务

```bash
crontab -e
```

添加以下内容：

```cron
# 每天凌晨 2 点备份
0 2 * * * /var/www/notes/backup.sh >> /var/log/notes/backup.log 2>&1

# 每小时检查服务状态
0 * * * * /var/www/notes/monitor.sh >> /var/log/notes/monitor.log 2>&1
```

### 3. 配置 SSL 证书（可选）

使用 Let's Encrypt 免费证书：

```bash
# 安装 Certbot
sudo yum install -y certbot python2-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
echo '0 12 * * * /usr/bin/certbot renew --quiet' | sudo crontab -
```

## 常用管理命令

### PM2 管理

```bash
pm2 status          # 查看应用状态
pm2 logs            # 查看日志
pm2 restart all     # 重启应用
pm2 stop all        # 停止应用
pm2 delete all      # 删除应用
```

### 服务管理

```bash
sudo systemctl status nginx    # 查看 Nginx 状态
sudo systemctl restart nginx  # 重启 Nginx
sudo systemctl status mysqld  # 查看 MySQL 状态
```

### 日志查看

```bash
# 应用日志
tail -f /var/log/notes/backend-combined.log

# Nginx 日志
tail -f /var/log/nginx/notes_access.log
tail -f /var/log/nginx/notes_error.log

# 系统监控
/var/www/notes/monitor.sh
```

## 更新应用

### 更新前端

```bash
/var/www/notes/update-frontend.sh
```

### 更新后端

```bash
cd /var/www/notes/backend
git pull origin main  # 或复制新文件
npm install
pm2 restart notes-backend
```

## 备份和恢复

### 手动备份

```bash
/var/www/notes/backup.sh
```

### 恢复数据库

```bash
mysql -u notes_user -p notes_db < /var/backups/notes/database_YYYYMMDD_HHMMSS.sql
```

## 故障排除

### 常见问题

1. **后端无法连接数据库**
   - 检查 MySQL 服务状态
   - 验证数据库用户和密码
   - 检查防火墙设置

2. **前端无法访问**
   - 检查 Nginx 配置
   - 验证域名解析
   - 检查防火墙端口

3. **PM2 应用崩溃**
   - 查看 PM2 日志：`pm2 logs`
   - 检查环境变量配置
   - 验证依赖安装

### 日志位置

- 后端日志：`/var/log/notes/`
- Nginx 日志：`/var/log/nginx/`
- MySQL 日志：`/var/log/mysqld.log`

## 安全建议

1. 定期更新系统和软件包
2. 使用强密码
3. 配置防火墙规则
4. 启用 HTTPS
5. 定期备份数据
6. 监控系统资源使用情况

## 支持

如果遇到问题，请检查：
1. 系统日志
2. 应用日志
3. 网络连接
4. 防火墙设置

或运行监控脚本查看系统状态：
```bash
/var/www/notes/monitor.sh
```
