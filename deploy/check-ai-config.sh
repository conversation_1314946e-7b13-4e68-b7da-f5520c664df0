#!/bin/bash

# AI 配置检查脚本
# 用于验证 Gemini API 配置是否正确

set -e

echo "=== AI 配置检查 ==="

# 检查前端环境变量文件
check_frontend_env() {
    echo "1. 检查前端环境变量..."
    
    if [ -f ".env" ]; then
        echo "✓ 找到 .env 文件"
        
        # 检查 GEMINI_API_KEY
        if grep -q "GEMINI_API_KEY=" .env; then
            api_key=$(grep "GEMINI_API_KEY=" .env | cut -d'=' -f2)
            if [ "$api_key" != "your_gemini_api_key_here" ] && [ -n "$api_key" ]; then
                echo "✓ GEMINI_API_KEY 已配置"
                
                # 检查 API 密钥格式
                if [[ $api_key =~ ^AIza[0-9A-Za-z_-]{35}$ ]]; then
                    echo "✓ API 密钥格式正确"
                else
                    echo "⚠ API 密钥格式可能不正确"
                fi
            else
                echo "✗ GEMINI_API_KEY 未配置或使用默认值"
                return 1
            fi
        else
            echo "✗ 未找到 GEMINI_API_KEY 配置"
            return 1
        fi
    else
        echo "✗ 未找到 .env 文件"
        return 1
    fi
}

# 检查部署目录的环境变量
check_deployed_env() {
    echo ""
    echo "2. 检查部署目录环境变量..."
    
    if [ -f "/var/www/notes/frontend/.env" ]; then
        echo "✓ 找到部署目录的 .env 文件"
        
        api_key=$(grep "GEMINI_API_KEY=" /var/www/notes/frontend/.env | cut -d'=' -f2)
        if [ "$api_key" != "your_gemini_api_key_here" ] && [ -n "$api_key" ]; then
            echo "✓ 部署目录的 GEMINI_API_KEY 已配置"
        else
            echo "✗ 部署目录的 GEMINI_API_KEY 未配置"
            return 1
        fi
    else
        echo "⚠ 部署目录未找到 .env 文件"
    fi
}

# 检查构建文件中的 API 密钥
check_build_files() {
    echo ""
    echo "3. 检查构建文件..."
    
    if [ -d "dist" ]; then
        echo "✓ 找到构建目录"
        
        # 检查构建文件中是否包含 API 密钥（不显示实际密钥）
        if find dist -name "*.js" -exec grep -l "AIza" {} \; | head -1 > /dev/null 2>&1; then
            echo "✓ 构建文件中包含 API 密钥"
        else
            echo "⚠ 构建文件中未找到 API 密钥，可能配置有误"
        fi
    else
        echo "⚠ 未找到构建目录，请先运行构建"
    fi
}

# 测试 API 连接
test_api_connection() {
    echo ""
    echo "4. 测试 API 连接..."
    
    if [ -f ".env" ]; then
        api_key=$(grep "GEMINI_API_KEY=" .env | cut -d'=' -f2)
        
        if [ -n "$api_key" ] && [ "$api_key" != "your_gemini_api_key_here" ]; then
            echo "正在测试 Gemini API 连接..."
            
            # 使用 curl 测试 API
            response=$(curl -s -w "%{http_code}" -o /tmp/gemini_test.json \
                -H "Content-Type: application/json" \
                -d '{"contents":[{"parts":[{"text":"Hello"}]}]}' \
                "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$api_key")
            
            if [ "$response" = "200" ]; then
                echo "✓ API 连接测试成功"
            else
                echo "✗ API 连接测试失败 (HTTP $response)"
                if [ -f "/tmp/gemini_test.json" ]; then
                    echo "错误详情:"
                    cat /tmp/gemini_test.json
                fi
                return 1
            fi
            
            # 清理临时文件
            rm -f /tmp/gemini_test.json
        else
            echo "⚠ 跳过 API 连接测试（未配置有效密钥）"
        fi
    else
        echo "⚠ 跳过 API 连接测试（未找到 .env 文件）"
    fi
}

# 提供修复建议
provide_suggestions() {
    echo ""
    echo "=== 配置建议 ==="
    echo ""
    echo "如果 AI 功能无法正常工作，请检查："
    echo ""
    echo "1. 获取 API 密钥："
    echo "   - 访问: https://aistudio.google.com/app/apikey"
    echo "   - 登录 Google 账户"
    echo "   - 创建新的 API 密钥"
    echo ""
    echo "2. 配置环境变量："
    echo "   - 编辑项目根目录的 .env 文件"
    echo "   - 设置 GEMINI_API_KEY=你的实际密钥"
    echo ""
    echo "3. 重新构建和部署："
    echo "   - npm run build"
    echo "   - ./deploy/frontend-deploy.sh"
    echo ""
    echo "4. 检查网络连接："
    echo "   - 确保服务器可以访问 generativelanguage.googleapis.com"
    echo "   - 检查防火墙设置"
    echo ""
    echo "5. 监控 API 使用："
    echo "   - 访问 Google Cloud Console"
    echo "   - 检查 API 配额和使用情况"
}

# 主函数
main() {
    local has_error=0
    
    # 检查当前目录
    if [ ! -f "package.json" ]; then
        echo "错误: 请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行检查
    check_frontend_env || has_error=1
    check_deployed_env || has_error=1
    check_build_files
    test_api_connection || has_error=1
    
    echo ""
    if [ $has_error -eq 0 ]; then
        echo "✓ AI 配置检查通过"
    else
        echo "✗ AI 配置存在问题"
        provide_suggestions
        exit 1
    fi
}

# 运行主函数
main
