#!/bin/bash

# Notes 应用一键部署脚本
# 适用于 CentOS 7 系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -eq 0 ]; then
        print_error "请不要使用 root 用户运行此脚本"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    if [ ! -f /etc/redhat-release ]; then
        print_error "此脚本仅支持 CentOS 7"
        exit 1
    fi
    
    if ! grep -q "CentOS Linux release 7" /etc/redhat-release; then
        print_warning "检测到非 CentOS 7 系统，可能存在兼容性问题"
    fi
}

# 显示欢迎信息
show_welcome() {
    echo "=================================================="
    echo "       Notes 应用 CentOS 7 部署脚本"
    echo "=================================================="
    echo ""
    echo "此脚本将自动完成以下操作："
    echo "1. 安装系统依赖 (Node.js, MySQL, Nginx, PM2)"
    echo "2. 配置数据库"
    echo "3. 部署后端应用"
    echo "4. 构建和部署前端"
    echo "5. 配置系统服务"
    echo ""
    echo "请确保："
    echo "- 系统已连接互联网"
    echo "- 当前用户具有 sudo 权限"
    echo "- 在项目根目录运行此脚本"
    echo ""
}

# 主菜单
show_menu() {
    echo ""
    echo "请选择部署选项："
    echo "1) 完整部署 (推荐新服务器)"
    echo "2) 仅环境准备"
    echo "3) 仅数据库配置"
    echo "4) 仅后端部署"
    echo "5) 仅前端部署"
    echo "6) 仅系统配置"
    echo "7) 查看部署状态"
    echo "0) 退出"
    echo ""
    read -p "请输入选项 (0-7): " choice
}

# 检查部署状态
check_status() {
    print_info "检查部署状态..."
    
    echo ""
    echo "=== 服务状态 ==="
    
    # 检查 MySQL
    if systemctl is-active --quiet mysqld 2>/dev/null; then
        print_success "MySQL: 运行中"
    else
        print_warning "MySQL: 未运行"
    fi
    
    # 检查 Nginx
    if systemctl is-active --quiet nginx 2>/dev/null; then
        print_success "Nginx: 运行中"
    else
        print_warning "Nginx: 未运行"
    fi
    
    # 检查 PM2
    if command -v pm2 &> /dev/null; then
        if pm2 list | grep -q "notes-backend.*online" 2>/dev/null; then
            print_success "后端应用: 运行中"
        else
            print_warning "后端应用: 未运行"
        fi
    else
        print_warning "PM2: 未安装"
    fi
    
    echo ""
    echo "=== 文件状态 ==="
    
    # 检查部署文件
    if [ -d "/var/www/notes/backend" ]; then
        print_success "后端文件: 已部署"
    else
        print_warning "后端文件: 未部署"
    fi
    
    if [ -d "/var/www/notes/frontend/dist" ]; then
        print_success "前端文件: 已部署"
    else
        print_warning "前端文件: 未部署"
    fi
    
    # 检查配置文件
    if [ -f "/etc/nginx/conf.d/notes.conf" ]; then
        print_success "Nginx 配置: 已配置"
    else
        print_warning "Nginx 配置: 未配置"
    fi
}

# 执行部署步骤
run_step() {
    local script_name=$1
    local step_name=$2
    
    if [ -f "deploy/$script_name" ]; then
        print_info "执行: $step_name"
        chmod +x "deploy/$script_name"
        bash "deploy/$script_name"
        print_success "$step_name 完成"
    else
        print_error "未找到脚本: deploy/$script_name"
        exit 1
    fi
}

# 完整部署
full_deploy() {
    print_info "开始完整部署..."
    
    run_step "centos7-setup.sh" "环境准备"
    echo ""
    read -p "环境准备完成，按回车键继续..."
    
    run_step "database-setup.sh" "数据库配置"
    echo ""
    read -p "数据库配置完成，按回车键继续..."
    
    run_step "backend-deploy.sh" "后端部署"
    echo ""
    read -p "后端部署完成，按回车键继续..."
    
    run_step "frontend-deploy.sh" "前端部署"
    echo ""
    read -p "前端部署完成，按回车键继续..."
    
    run_step "system-config.sh" "系统配置"
    
    print_success "完整部署完成！"
    
    echo ""
    echo "=== 部署完成 ==="
    echo "请完成以下最后步骤："
    echo "1. 运行 'pm2 startup' 并执行显示的命令"
    echo "2. 配置环境变量："
    echo "   - 后端: 编辑 /var/www/notes/backend/.env"
    echo "   - 前端: 确保 Gemini API 密钥已正确配置"
    echo "3. 修改域名 DNS 解析到此服务器"
    echo "4. 访问 http://your-domain.com 测试应用"
    echo "5. 测试 AI 功能是否正常工作"
    echo "6. 可选：配置 SSL 证书启用 HTTPS"
}

# 主程序
main() {
    check_root
    check_system
    show_welcome
    
    while true; do
        show_menu
        
        case $choice in
            1)
                full_deploy
                break
                ;;
            2)
                run_step "centos7-setup.sh" "环境准备"
                ;;
            3)
                run_step "database-setup.sh" "数据库配置"
                ;;
            4)
                run_step "backend-deploy.sh" "后端部署"
                ;;
            5)
                run_step "frontend-deploy.sh" "前端部署"
                ;;
            6)
                run_step "system-config.sh" "系统配置"
                ;;
            7)
                check_status
                ;;
            0)
                print_info "退出部署脚本"
                exit 0
                ;;
            *)
                print_error "无效选项，请重新选择"
                ;;
        esac
        
        echo ""
        read -p "按回车键返回主菜单..."
    done
}

# 运行主程序
main
